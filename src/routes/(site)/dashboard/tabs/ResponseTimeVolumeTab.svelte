<script lang="ts">
    import LineChart from '$lib/components/dashboard/LineChart.svelte';
    import ScoreCard from '$lib/components/dashboard/ScoreCard.svelte';
    import { COLORS } from '$lib/components/dashboard/colors';
    import { t, dict } from '$lib/stores/i18n';
    import { onMount } from 'svelte';
    import thLocaleData from '$lib/locales/th.json';
    import { currentLanguage, languagePreference } from '$lib/stores/languagePreference';
    import { ExpandOutline, DownloadOutline } from 'flowbite-svelte-icons';

    // Define API Base URL
    import { getBackendUrl } from '$src/lib/config';

    // Define props for startDate and endDate
    export let startDate: string | undefined;
    export let endDate: string | undefined;

    // Data state
    let totalIncomingMessages: number | null = null;
    let totalIncomingMessagesTrend: number | null = null;
    let totalIncomingTickets: number | null = null;
    let totalIncomingTicketsTrend: number | null = null;
    let dailyIncomingChatVolume: LineChartData[] = [];
    let incomingMessagesByTimeSlot: TimeSlotDataItem[] = [];

    // Loading and error states
    let isLoadingDailyVolume: boolean = true;
    let dailyVolumeError: string | null = null;
    let isLoadingMessagesByTimeSlot: boolean = true;
    let messagesByTimeSlotError: string | null = null;
    let isLoadingTotalTickets: boolean = true;
    let totalTicketsError: string | null = null;
    let isLoadingTotalMessages: boolean = true;
    let totalMessagesError: string | null = null;

    // State for expanded chart/table modals
    let isDailyVolumeExpanded: boolean = false;
    let isMessagesByTimeSlotExpanded: boolean = false;

    // Interfaces for API responses and component props
    interface LineChartData {
        label: string;
        value: number;
    }

    interface TimeSlotDataItem {
        time_slot: string;
        monday: number;
        tuesday: number;
        wednesday: number;
        thursday: number;
        friday: number;
        saturday: number;
        sunday: number;
    }

    // API Response Interfaces
    interface IncomingMessageCountTimeSeriesResponse {
        time: string;
        incoming_message_count: number;
    }

    interface IncomingMessageCountResponse {
        main_period: {
            start_date: string;
            end_date: string;
            metric_value: number | null;
            time_series_data: { date: string; value: number }[];
        };
        comparison_period: {
            start_date: string;
            end_date: string;
            metric_value: number | null;
            time_series_data: { date: string; value: number }[];
        };
        percentage_change: number | null;
        units: string;
    }

    interface TicketTotalCountResponse {
        main_period: {
            start_date: string;
            end_date: string;
            metric_value_name: string;
            metric_value: number | null;
            time_series_data: { date: string; value: number | null }[];
            detailed_tickets: any[];
        };
        comparison_period: {
            start_date: string;
            end_date: string;
            metric_value_name: string;
            metric_value: number | null;
            time_series_data: { date: string; value: number | null }[];
        };
        percentage_change: number | null;
        units: string;
    }

    // Sort state for the table
    let currentSort: { [key: string]: { column: string; direction: 'asc' | 'desc' | null } } = {
        incomingMessagesByTimeSlot: { column: 'time_slot', direction: 'asc' },
    };

    // Reactive variable to determine if the current language is Thai
    $: isThaiLocale = $dict === thLocaleData;

    function sortTable<T>(dataArray: T[], key: keyof T, tableName: string): T[] {
        const currentTableSort = currentSort[tableName] || { column: null, direction: null };
        let newDirection: 'asc' | 'desc' = 'asc';

        if (currentTableSort.column === key) {
            newDirection = currentTableSort.direction === 'asc' ? 'desc' : 'asc';
        }

        currentSort = { ...currentSort, [tableName]: { column: String(key), direction: newDirection } };

        return [...dataArray].sort((a, b) => {
            const aValue = a[key];
            const bValue = b[key];

            if (typeof aValue === 'string' && typeof bValue === 'string') {
                if (key === 'time_slot') {
                    const parseTime = (timeStr: string) => {
                        const [start, end] = timeStr.split('-').map(s => parseInt(s.split(':')[0]));
                        return start * 60 + (end === 0 ? 24 * 60 : end * 60);
                    };
                    const timeA = parseTime(aValue);
                    const timeB = parseTime(bValue);
                    return newDirection === 'asc' ? timeA - timeB : timeB - timeA;
                }
                return newDirection === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
            } else if (typeof aValue === 'number' && typeof bValue === 'number') {
                return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
            }
            return 0;
        });
    }

    function applyInitialSort<T>(dataArray: T[], tableName: string): T[] {
        const sortState = currentSort[tableName];
        if (sortState && sortState.column) {
            return [...dataArray].sort((a, b) => {
                const key = sortState.column as keyof T;
                const aValue = a[key];
                const bValue = b[key];
                const direction = sortState.direction;
                if (typeof aValue === 'string' && typeof bValue === 'string') {
                    if (key === 'time_slot') {
                        const parseTime = (timeStr: string) => {
                            const [start, end] = timeStr.split('-').map(s => parseInt(s.split(':')[0]));
                            return start * 60 + (end === 0 ? 24 * 60 : end * 60);
                        };
                        const timeA = parseTime(aValue);
                        const timeB = parseTime(bValue);
                        return direction === 'asc' ? timeA - timeB : timeB - timeA;
                    }
                    return direction === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
                } else if (typeof aValue === 'number' && typeof bValue === 'number') {
                    return direction === 'asc' ? aValue - bValue : bValue - aValue;
                }
                return 0;
            });
        }
        return dataArray;
    }

    async function fetchData() {
        console.log("fetchData called for ResponseTimeVolumeTab");
        console.log(`Current filter settings (from props): startDate='${startDate}', endDate='${endDate}'`);

        // Reset loading and error states for all components
        isLoadingTotalTickets = true;
        isLoadingTotalMessages = true;
        isLoadingMessagesByTimeSlot = true;
        isLoadingDailyVolume = true;

        totalTicketsError = null;
        totalMessagesError = null;
        messagesByTimeSlotError = null;
        dailyVolumeError = null;

        // Build query params using URLSearchParams
        const urlParams = new URLSearchParams();

        if (startDate) urlParams.append('start_date', startDate);
        if (endDate) urlParams.append('end_date', endDate);

        // Only add lang=th if language is Thai
        const currentLang = languagePreference.getCurrentLanguage();
        if (currentLang === 'th') {
            urlParams.append('lang', 'th');
        }

        const queryString = urlParams.toString();
        const baseUrl = getBackendUrl();

        // Define all fetch promises
        const endpoints = [
            'incoming-message-count',
            'ticket-total-count',
            'customer-message-heatmap',
            'incoming-message-count-time-series'
        ];

        const requests = endpoints.map(endpoint =>
            fetch(`${baseUrl}/dashboard/api/${endpoint}/?${queryString}`)
        );

        const [
            incomingMessageCountResult,
            ticketTotalCountResult,
            customerMessageHeatmapResult,
            incomingMessageCountTimeSeriesResult
        ] = await Promise.allSettled(requests);      
        
    
        // Process Incoming Message Count (Scorecard)
        if (incomingMessageCountResult.status === 'fulfilled' && incomingMessageCountResult.value.ok) {
            try {
                const data = await incomingMessageCountResult.value.json();
                totalIncomingMessages = data.main_period.metric_value ?? null;
                totalIncomingMessagesTrend = data.percentage_change ?? null;
            } catch (error) {
                console.error('Error parsing incoming message count data:', error);
                totalMessagesError = 'Error fetching data.';
            }
        } else {
            console.error('Failed to fetch incoming message count:', incomingMessageCountResult.status === 'rejected' ? incomingMessageCountResult.reason : `HTTP error: ${incomingMessageCountResult.value?.status}`);
            totalMessagesError = 'No data available.';
        }
        isLoadingTotalMessages = false;

        // Process Total Tickets (Scorecard)
        if (ticketTotalCountResult.status === 'fulfilled' && ticketTotalCountResult.value.ok) {
            try {
                const data = await ticketTotalCountResult.value.json();
                totalIncomingTickets = data.main_period.metric_value ?? null;
                totalIncomingTicketsTrend = data.percentage_change ?? null;
            } catch (error) {
                console.error('Error parsing total tickets data:', error);
                totalTicketsError = 'Error fetching data.';
            }
        } else {
            console.error('Failed to fetch total tickets:', ticketTotalCountResult.status === 'rejected' ? ticketTotalCountResult.reason : `HTTP error: ${ticketTotalCountResult.value?.status}`);
            totalTicketsError = 'No data available.';
        }
        isLoadingTotalTickets = false;

        // Process Incoming Messages by Time Slot (Table)
        if (customerMessageHeatmapResult.status === 'fulfilled' && customerMessageHeatmapResult.value.ok) {
            try {
                const data: TimeSlotDataItem[] = await customerMessageHeatmapResult.value.json();
                if (Array.isArray(data) && data.length > 0) {
                    incomingMessagesByTimeSlot = applyInitialSort(data, 'incomingMessagesByTimeSlot');
                } else {
                    incomingMessagesByTimeSlot = [];
                    messagesByTimeSlotError = 'No data available.';
                }
            } catch (error) {
                console.error('Error parsing customer message heatmap data:', error);
                incomingMessagesByTimeSlot = [];
                messagesByTimeSlotError = 'Error fetching data.';
            }
        } else {
            console.error('Failed to fetch customer message heatmap:', customerMessageHeatmapResult.status === 'rejected' ? customerMessageHeatmapResult.reason : `HTTP error: ${customerMessageHeatmapResult.value?.status}`);
            incomingMessagesByTimeSlot = [];
            messagesByTimeSlotError = 'No data available.';
        }
        isLoadingMessagesByTimeSlot = false;

        // Process Daily Incoming Message Volume (Line Chart)
        if (incomingMessageCountTimeSeriesResult.status === 'fulfilled' && incomingMessageCountTimeSeriesResult.value.ok) {
            try {
                const data: IncomingMessageCountTimeSeriesResponse[] = await incomingMessageCountTimeSeriesResult.value.json();
                if (Array.isArray(data) && data.length > 0) {
                    dailyIncomingChatVolume = data.map(item => ({
                        label: new Date(item.time).toLocaleDateString(
                            isThaiLocale ? 'th-TH-u-ca-buddhist' : 'en-US',
                            { month: 'short', day: 'numeric'}
                        ),
                        value: item.incoming_message_count
                    }));
                } else {
                    dailyIncomingChatVolume = [];
                    dailyVolumeError = 'No data available.';
                }
            } catch (error) {
                console.error('Error parsing daily incoming chat volume data:', error);
                dailyIncomingChatVolume = [];
                dailyVolumeError = 'Error fetching data.';
            }
        } else {
            console.error('Failed to fetch daily incoming chat volume:', incomingMessageCountTimeSeriesResult.status === 'rejected' ? incomingMessageCountTimeSeriesResult.reason : `HTTP error: ${incomingMessageCountTimeSeriesResult.value?.status}`);
            dailyIncomingChatVolume = [];
            dailyVolumeError = 'No data available.';
        }
        isLoadingDailyVolume = false;
    }

    function getMessageCountColorClass(count: number): string {
        if (count > 10) {
            return 'bg-green-600 text-white font-bold';
        } else if (count > 5) {
            return 'bg-green-400 text-gray-800';
        } else if (count > 0) {
            return 'bg-green-200 text-gray-700';
        } else {
            return 'bg-gray-100 text-gray-500';
        }
    }

    // Functions to add for downloading Excel files
    async function downloadDailyIncomingMessageVolumeExcel() {
        console.log("Attempting to download daily incoming chat volume excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = new URLSearchParams();

        if (startDate) urlParams.append('start_date', startDate);
        if (endDate) urlParams.append('end_date', endDate);

        // Append language param only if Thai
        if (currentLang === 'th') {
            urlParams.append('lang', 'th');
        }

        const query = urlParams.toString();
        const downloadUrl = `${getBackendUrl()}/dashboard/api/incoming-message-count-time-series.xlsx/?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${t('dbResponseTimeVolume.dailyIncomingChatVolumeExcel')}_${startDate}_${endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(t('db.excelAlert'));
        }
    }
    
    async function downloadMessagesByTimeSlotExcel() {
        console.log("Attempting to download incoming messages by time slot excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = new URLSearchParams();

        if (startDate) urlParams.append('start_date', startDate);
        if (endDate) urlParams.append('end_date', endDate);

        // Append language param only if Thai
        if (currentLang === 'th') {
            urlParams.append('lang', 'th');
        }

        const query = urlParams.toString();
        const downloadUrl = `${getBackendUrl()}/dashboard/api/customer-message-heatmap.xlsx/?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${t('dbResponseTimeVolume.incomingMessagesByTimeSlotExcel')}_${startDate}_${endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(t('db.excelAlert'));
        }
    }    

    onMount(() => {
        fetchData();
    });

    // Reactively re-fetch data when props or isThaiLocale change
    $: startDate, endDate, isThaiLocale, fetchData();
</script>

{#if isDailyVolumeExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbResponseTimeVolume.dailyIncomingChatVolume')}</h3>
                <div class="flex items-center gap-2">
                    <button on:click={() => isDailyVolumeExpanded = false} class="text-gray-500 hover:text-gray-700">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
            <div class="flex-grow w-full h-full">
                {#if isLoadingDailyVolume}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-full">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if dailyVolumeError}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <LineChart
                        data={dailyIncomingChatVolume}
                        chartLabel="Line"
                        lineColor={COLORS.green}
                        showDataLabels={true}
                        chartHeight="100%"
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}




{#if isMessagesByTimeSlotExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbResponseTimeVolume.incomingMessagesByTimeSlot')}</h3>
                <button on:click={() => isMessagesByTimeSlotExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="overflow-auto flex-grow w-full">
                {#if isLoadingMessagesByTimeSlot}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-48">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if messagesByTimeSlotError}
                    <div class="text-gray-600 text-center text-lg h-48 flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'time_slot', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.timeSlot')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'time_slot'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'monday', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.monday')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'monday'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'tuesday', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.tuesday')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'tuesday'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'wednesday', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.wednesday')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'wednesday'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'thursday', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.thursday')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'thursday'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'friday', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.friday')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'friday'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'saturday', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.saturday')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'saturday'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'sunday', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.sunday')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'sunday'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each incomingMessagesByTimeSlot as item}
                                <tr>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-sm font-medium text-gray-900">{item.time_slot}</td>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-sm {getMessageCountColorClass(item.monday)}">{item.monday}</td>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-sm {getMessageCountColorClass(item.tuesday)}">{item.tuesday}</td>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-sm {getMessageCountColorClass(item.wednesday)}">{item.wednesday}</td>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-sm {getMessageCountColorClass(item.thursday)}">{item.thursday}</td>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-sm {getMessageCountColorClass(item.friday)}">{item.friday}</td>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-sm {getMessageCountColorClass(item.saturday)}">{item.saturday}</td>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-sm {getMessageCountColorClass(item.sunday)}">{item.sunday}</td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
    </div>
{/if}

<div class="flex flex-col gap-2">
    <div class="grid grid-cols-1 xl:grid-cols-5 gap-2">
        <div class="lg:col-span-3 flex flex-col gap-2">
            <div class="grid grid-cols-1 gap-2"> <div class="bg-white rounded-lg shadow-md p-6">
                    <ScoreCard
                        title={t('dbResponseTimeVolume.totalIncomingMessages')}
                        value={totalIncomingMessages}
                        valueColor="text-black-600"
                        trendValue={totalIncomingMessagesTrend}
                        trendUnit="%"
                    />
                </div>
                <div class="bg-white rounded-lg shadow-md p-6">
                    <ScoreCard
                        title={t('dbResponseTimeVolume.totalTickets')}
                        value={totalIncomingTickets}
                        valueColor="text-black-600"
                        trendValue={totalIncomingTicketsTrend}
                        trendUnit="%" />
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                    <h2 class="text-xl font-semibold text-gray-700">{t('dbResponseTimeVolume.dailyIncomingChatVolume')}</h2>
                    <div class="flex gap-2 flex-wrap sm:flex-nowrap">
                        <button 
                            on:click={() => isDailyVolumeExpanded = true} 
                            class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                            <!-- {t('db.expand')} -->
                            <ExpandOutline class="h-5 w-5" />
                        </button>
                        <button
                            on:click={downloadDailyIncomingMessageVolumeExcel}
                            class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                            <!-- {t('db.downloadExcel')} -->
                            <DownloadOutline class="h-5 w-5" />
                        </button>
                    </div>
                </div>            
                <div class="w-full flex items-center justify-center">
                    {#if isLoadingDailyVolume}
                        <div class="flex flex-col items-center justify-center text-gray-600">
                            <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                        </div>
                    {:else if dailyVolumeError}
                        <div class="text-gray-600 text-center text-lg h-48 flex items-center justify-center">
                            {t('db.noDataAvailable')}
                        </div>
                    {:else}
                        <LineChart
                            data={dailyIncomingChatVolume}
                            chartLabel="Line"
                            lineColor={COLORS.green}
                            showDataLabels={true}
                            chartHeight="35rem"
                        />
                    {/if}
                </div>
            </div>


        </div>

        <div class="lg:col-span-2 bg-white p-6 rounded-lg shadow-md overflow-x-auto">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbResponseTimeVolume.incomingMessagesByTimeSlot')}</h2>
                <div class="flex gap-2 flex-wrap sm:flex-nowrap">
                    <button 
                        on:click={() => isMessagesByTimeSlotExpanded = true}
                        class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <!-- {t('db.expand')} -->
                        <ExpandOutline class="h-5 w-5" />
                    </button>
                    <button
                        on:click={downloadMessagesByTimeSlotExcel}
                        class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <!-- {t('db.downloadExcel')} -->
                        <DownloadOutline class="h-5 w-5" />
                    </button>
                </div>
            </div>            
            <div class="overflow-x-auto overflow-y-auto">
                {#if isLoadingMessagesByTimeSlot}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-48">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if messagesByTimeSlotError}
                    <div class="text-gray-600 text-center text-lg h-48 flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'time_slot', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.timeSlot')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'time_slot'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'monday', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.monday')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'monday'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'tuesday', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.tuesday')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'tuesday'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'wednesday', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.wednesday')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'wednesday'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'thursday', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.thursday')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'thursday'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'friday', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.friday')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'friday'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'saturday', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.saturday')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'saturday'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'sunday', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.sunday')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'sunday'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white">
                            {#each incomingMessagesByTimeSlot as item}
                                <tr>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-center text-sm font-medium text-gray-900">{item.time_slot}</td>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-center text-sm {getMessageCountColorClass(item.monday)}">{item.monday}</td>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-center text-sm {getMessageCountColorClass(item.tuesday)}">{item.tuesday}</td>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-center text-sm {getMessageCountColorClass(item.wednesday)}">{item.wednesday}</td>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-center text-sm {getMessageCountColorClass(item.thursday)}">{item.thursday}</td>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-center text-sm {getMessageCountColorClass(item.friday)}">{item.friday}</td>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-center text-sm {getMessageCountColorClass(item.saturday)}">{item.saturday}</td>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-center text-sm {getMessageCountColorClass(item.sunday)}">{item.sunday}</td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
    </div>
</div>