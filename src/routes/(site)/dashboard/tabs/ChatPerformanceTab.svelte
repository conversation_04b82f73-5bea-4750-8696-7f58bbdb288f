<script lang="ts">
    import BarChart from '$lib/components/dashboard/BarChart.svelte';
    import ScoreCard from '$lib/components/dashboard/ScoreCard.svelte';
    import { COLORS } from '$lib/components/dashboard/colors';
    import { t } from '$lib/stores/i18n';
    import { currentLanguage, languagePreference } from '$lib/stores/languagePreference';
    import { onMount } from 'svelte';
    import { ExpandOutline, DownloadOutline } from 'flowbite-svelte-icons';

    // Define API Base URL
    import { getBackendUrl } from '$src/lib/config';

    // Define props for startDate and endDate
    export let startDate: string | undefined;
    export let endDate: string | undefined;

    // Data fetched from backend - Scorecard values
    let allTickets: number | null = null;
    let allTicketsTrend: number | null = null;
    let allClosedTickets: number | null = null;
    let allClosedTicketsTrend: number | null = null;
    let chatVolume: number | null = null;
    let chatVolumeTrend: number | null = null;
    let closedRate: number | null = null;
    let closedRateTrend: number | null = null;
    let avgHandlingTime: number | null = null;
    let handlingTimeRate: number | null = null;
    let avgResponseTime: number | null = null;
    let responseTimeTrend: number | null = null;
    let responseRateWithin6s: number | null = null;
    let responseRateWithin6sTrend: number | null = null;
    let handlingRateWithin5mins: number | null = null;
    let handlingRateWithin5minsTrend: number | null = null;

    // Data fetched from backend - Charts and tables
    let ticketStatusData: TicketStatusDataItem[] = [];
    let closedTicketsByCaseType: CaseTypeDataItem[] = [];
    let closedTicketsBySubCaseType: SubCaseTypeDataItem[] = [];
    let closedCaseSubCaseTable: CaseSubCaseInfo[] = [];

    // Loading and error states
    let isLoadingTicketStatusChart: boolean = true;
    let isLoadingUnclosedTickets: boolean = true;
    let isLoadingClosedTickets: boolean = true;
    let isLoadingCaseTypeChart: boolean = true;
    let isLoadingSubCaseTypeChart: boolean = true;
    let isLoadingCaseSubCaseTable: boolean = true;
    let isLoadingScorecards: boolean = true;

    // Specific error messages for charts/tables, set to generic for frontend
    let ticketStatusChartError: string | null = null;
    let unclosedTicketsError: string | null = null;
    let closedTicketsError: string | null = null;
    let caseTypeChartError: string | null = null;
    let subCaseTypeChartError: string | null = null;
    let caseSubCaseTableError: string | null = null;

    // Expand state variables for modals
    let isTicketStatusChartExpanded: boolean = false;
    let isUnclosedTicketsExpanded: boolean = false;
    let isClosedTicketsExpanded: boolean = false;
    let isCaseTypeChartExpanded: boolean = false;
    let isSubCaseTypeChartExpanded: boolean = false;
    let isCaseSubCaseTableExpanded: boolean = false;

    // Define API response interfaces
    interface MetricAPIResponse {
        main_period: {
            start_date: string;
            end_date: string;
            metric_value: number | null;
            time_series_data: { date: string; value: number | null }[];
        };
        comparison_period: {
            start_date: string;
            end_date: string;
            metric_value: number | null;
            time_series_data: { date: string; value: number | null }[];
        };
        percentage_change: number | null;
        units: string;
    }

    interface TicketStatusAPIResponse {
        status: string;
        ticket_count: number;
    }

    interface ClosedTicketsByCaseTypeAPIResponse {
        case_type: string;
        ticket_count: number;
    }

    interface ClosedTicketsBySubCaseTypeAPIResponse {
        case_topic: string;
        ticket_count: number;
    }

    interface ClosedTicketsByCaseTypeAndTopicAPIResponse {
        case_type: string;
        case_topic: string;
        ticket_count: number;
    }

    // --- Interfaces for other visualizations ---
    interface TicketStatusDataItem {
        status: string;
        amount: number;
    }

    interface CaseTypeDataItem {
        caseType: string;
        count: number;
    }

    interface SubCaseTypeDataItem {
        subCaseType: string;
        count: number;
    }

    interface CaseSubCaseInfo {
        caseType: string;
        subCaseType: string;
        count: number;
    }

    // SORTING STATE AND FUNCTIONALITY
    let currentSort: { [key: string]: { column: string; direction: 'asc' | 'desc' | null } } = {
        unclosedTickets: { column: 'totalUsedTime', direction: 'desc' },
        closedTickets: { column: 'totalUsedTime', direction: 'desc' },
        closedCaseSubCaseTable: { column: 'count', direction: 'desc' },
    };

    function sortTable<T>(dataArray: T[], key: keyof T, tableName: string): T[] {
        const currentTableSort = currentSort[tableName] || { column: null, direction: null };
        let newDirection: 'asc' | 'desc' = 'asc';

        if (currentTableSort.column === key) {
            newDirection = currentTableSort.direction === 'asc' ? 'desc' : 'asc';
        }

        currentSort = {
            ...currentSort,
            [tableName]: { column: String(key), direction: newDirection }
        };

        return [...dataArray].sort((a, b) => {
            const aValue = a[key];
            const bValue = b[key];

            if (typeof aValue === 'string' && typeof bValue === 'string') {
                return newDirection === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
            } else if (typeof aValue === 'number' && typeof bValue === 'number') {
                return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
            }
            return 0;
        });
    }

    function applyInitialSort<T>(dataArray: T[], tableName: string): T[] {
        const sortState = currentSort[tableName];
        if (sortState && sortState.column) {
            return [...dataArray].sort((a, b) => {
                const key = sortState.column as keyof T;
                const aValue = a[key];
                const bValue = b[key];
                const direction = sortState.direction;

                if (typeof aValue === 'string' && typeof bValue === 'string') {
                    return direction === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
                } else if (typeof aValue === 'number' && typeof bValue === 'number') {
                    return direction === 'asc' ? aValue - bValue : bValue - aValue;
                }
                return 0;
            });
        }
        return dataArray;
    }

    const setValueOrNull = (value: number | null): number | null => {
        if (value === 0) {
            return null;
        }
        return value;
    };

    const setTrendOrNull = (mainValue: number | null, trendValueFromAPI: number | null): number | null => {
        if (mainValue === null) {
            return null;
        }
        return trendValueFromAPI;
    };

    async function fetchData() {
        console.log("fetchData called for Chat Performance dashboards/tables");
        console.log(`Current filter settings (from props): startDate='${startDate}', endDate='${endDate}'`);

        isLoadingTicketStatusChart = true;
        isLoadingUnclosedTickets = true;
        isLoadingClosedTickets = true;
        isLoadingCaseTypeChart = true;
        isLoadingSubCaseTypeChart = true;
        isLoadingCaseSubCaseTable = true;
        isLoadingScorecards = true;

        ticketStatusChartError = null;
        unclosedTicketsError = null;
        closedTicketsError = null;
        caseTypeChartError = null;
        subCaseTypeChartError = null;
        caseSubCaseTableError = null;

        chatVolume = null;
        chatVolumeTrend = null;
        allTickets = null;
        allTicketsTrend = null;
        allClosedTickets = null;
        allClosedTicketsTrend = null;
        closedRate = null;
        closedRateTrend = null;
        avgResponseTime = null;
        responseTimeTrend = null;
        responseRateWithin6s = null;
        responseRateWithin6sTrend = null;
        avgHandlingTime = null;
        handlingTimeRate = null;
        handlingRateWithin5mins = null;
        handlingRateWithin5minsTrend = null;

        // Build query params using URLSearchParams
        const urlParams = new URLSearchParams();

        if (startDate) urlParams.append('start_date', startDate);
        if (endDate) urlParams.append('end_date', endDate);

        // Only add lang=th if language is Thai
        const currentLang = languagePreference.getCurrentLanguage();
        if (currentLang === 'th') {
            urlParams.append('lang', 'th');
        }

        const queryString = urlParams.toString();
        const baseUrl = getBackendUrl();

        // Define all fetch promises
        const endpoints = [
            'incoming-message-count',
            'distinct-incoming-tickets-count',
            'closed-ticket-count',
            'closed-ticket-rate',
            'average-response-time',
            '6second-response-rate',
            'average-handling-time',
            'handling-rate-within-5min',
            'ticket-status-count',
            'closed-tickets-by-case-type',
            'closed-tickets-by-case-topic',
            'closed-tickets-by-case-type-and-topic'
        ];

        const requests = endpoints.map(endpoint =>
            fetch(`${baseUrl}/dashboard/api/${endpoint}/?${queryString}`)
        );

        const [
            incomingMessagesResult,
            distinctTicketsResult,
            closedTicketsCountResult,
            closedTicketRateResult,
            avgResponseTimeResult,
            responseRateResult,
            avgHandlingTimeResult,
            handlingRateResult,
            ticketStatusResult,
            closedTicketsByCaseTypeResult,
            closedTicketsBySubCaseTypeResult,
            closedTicketsByCaseAndTopicResult
        ] = await Promise.allSettled(requests);


        // Process Incoming Messages ScoreCard
        if (incomingMessagesResult.status === 'fulfilled' && incomingMessagesResult.value.ok) {
            try {
                const data: MetricAPIResponse = await incomingMessagesResult.value.json();
                chatVolume = setValueOrNull(data.main_period.metric_value);
                chatVolumeTrend = setTrendOrNull(chatVolume, data.percentage_change);
            } catch (error) {
                console.error('Error parsing total incoming messages data:', error);
                chatVolume = null;
                chatVolumeTrend = null;
            }
        } else {
            console.error('Failed to fetch total incoming messages data:', incomingMessagesResult.status === 'rejected' ? incomingMessagesResult.reason : `HTTP error: ${incomingMessagesResult.value?.status}`);
            chatVolume = null;
            chatVolumeTrend = null;
        }

        // Process Total Agent Tickets ScoreCard
        if (distinctTicketsResult.status === 'fulfilled' && distinctTicketsResult.value.ok) {
            try {
                const data: MetricAPIResponse = await distinctTicketsResult.value.json();
                allTickets = setValueOrNull(data.main_period.metric_value);
                allTicketsTrend = setTrendOrNull(allTickets, data.percentage_change);
            } catch (error) {
                console.error('Error parsing total agent tickets data:', error);
                allTickets = null;
                allTicketsTrend = null;
            }
        } else {
            console.error('Failed to fetch total agent tickets data:', distinctTicketsResult.status === 'rejected' ? distinctTicketsResult.reason : `HTTP error: ${distinctTicketsResult.value?.status}`);
            allTickets = null;
            allTicketsTrend = null;
        }

        // Process Total Agent Closed Tickets ScoreCard
        if (closedTicketsCountResult.status === 'fulfilled' && closedTicketsCountResult.value.ok) {
            try {
                const data: MetricAPIResponse = await closedTicketsCountResult.value.json();
                allClosedTickets = setValueOrNull(data.main_period.metric_value);
                allClosedTicketsTrend = setTrendOrNull(allClosedTickets, data.percentage_change);
            } catch (error) {
                console.error('Error parsing total agent closed tickets data:', error);
                allClosedTickets = null;
                allClosedTicketsTrend = null;
            }
        } else {
            console.error('Failed to fetch total agent closed tickets data:', closedTicketsCountResult.status === 'rejected' ? closedTicketsCountResult.reason : `HTTP error: ${closedTicketsCountResult.value?.status}`);
            allClosedTickets = null;
            allClosedTicketsTrend = null;
        }

        // Process Agent Ticket Closure Rate vs Incoming (%) ScoreCard
        if (closedTicketRateResult.status === 'fulfilled' && closedTicketRateResult.value.ok) {
            try {
                const data: MetricAPIResponse = await closedTicketRateResult.value.json();
                closedRate = data.main_period.metric_value;
                closedRateTrend = setTrendOrNull(closedRate, data.percentage_change);
            } catch (error) {
                console.error('Error parsing closed ticket rate data:', error);
                closedRate = null;
                closedRateTrend = null;
            }
        } else {
            console.error('Failed to fetch closed ticket rate data:', closedTicketRateResult.status === 'rejected' ? closedTicketRateResult.reason : `HTTP error: ${closedTicketRateResult.value?.status}`);
            closedRate = null;
            closedRateTrend = null;
        }

        // Process Average Agent Response Time (seconds) ScoreCard
        if (avgResponseTimeResult.status === 'fulfilled' && avgResponseTimeResult.value.ok) {
            try {
                const data: MetricAPIResponse = await avgResponseTimeResult.value.json();
                avgResponseTime = data.main_period.metric_value;
                responseTimeTrend = setTrendOrNull(avgResponseTime, data.percentage_change);
            } catch (error) {
                console.error('Error fetching average response time data:', error);
                avgResponseTime = null;
                responseTimeTrend = null;
            }
        } else {
            console.error('Failed to fetch average response time data:', avgResponseTimeResult.status === 'rejected' ? avgResponseTimeResult.reason : `HTTP error: ${avgResponseTimeResult.value?.status}`);
            avgResponseTime = null;
            responseTimeTrend = null;
        }

        // Process Agent Response Rate Within 6 Seconds (%) ScoreCard
        if (responseRateResult.status === 'fulfilled' && responseRateResult.value.ok) {
            try {
                const data: MetricAPIResponse = await responseRateResult.value.json();
                responseRateWithin6s = setValueOrNull(data.main_period.metric_value);
                responseRateWithin6sTrend = setTrendOrNull(responseRateWithin6s, data.percentage_change);
            } catch (error) {
                console.error('Error fetching 6-second response rate data:', error);
                responseRateWithin6s = null;
                responseRateWithin6sTrend = null;
            }
        } else {
            console.error('Failed to fetch 6-second response rate data:', responseRateResult.status === 'rejected' ? responseRateResult.reason : `HTTP error: ${responseRateResult.value?.status}`);
            responseRateWithin6s = null;
            responseRateWithin6sTrend = null;
        }

        // Process Average Agent Handling Time (minutes) ScoreCard
        if (avgHandlingTimeResult.status === 'fulfilled' && avgHandlingTimeResult.value.ok) {
            try {
                const data: MetricAPIResponse = await avgHandlingTimeResult.value.json();
                avgHandlingTime = data.main_period.metric_value;
                handlingTimeRate = setTrendOrNull(avgHandlingTime, data.percentage_change);
            } catch (error) {
                console.error('Error fetching average handling time data:', error);
                avgHandlingTime = null;
                handlingTimeRate = null;
            }
        } else {
            console.error('Failed to fetch average handling time data:', avgHandlingTimeResult.status === 'rejected' ? avgHandlingTimeResult.reason : `HTTP error: ${avgHandlingTimeResult.value?.status}`);
            avgHandlingTime = null;
            handlingTimeRate = null;
        }

        // Process Agent Handling Rate Within 5 Minutes (%) ScoreCard
        if (handlingRateResult.status === 'fulfilled' && handlingRateResult.value.ok) {
            try {
                const data: MetricAPIResponse = await handlingRateResult.value.json();
                handlingRateWithin5mins = data.main_period.metric_value;
                handlingRateWithin5minsTrend = setTrendOrNull(handlingRateWithin5mins, data.percentage_change);
            } catch (error) {
                console.error('Error fetching 5-minute handling rate data:', error);
                handlingRateWithin5mins = null;
                handlingRateWithin5minsTrend = null;
            }
        } else {
            console.error('Failed to fetch 5-minute handling rate data:', handlingRateResult.status === 'rejected' ? handlingRateResult.reason : `HTTP error: ${handlingRateResult.value?.status}`);
            handlingRateWithin5mins = null;
            handlingRateWithin5minsTrend = null;
        }
        
        isLoadingScorecards = false;

        // Process Ticket Status Chart
        if (ticketStatusResult.status === 'fulfilled' && ticketStatusResult.value.ok) {
            try {
                const data: TicketStatusAPIResponse[] = await ticketStatusResult.value.json();
                if (Array.isArray(data) && data.length > 0) {
                    ticketStatusData = data.map(item => ({ status: item.status, amount: item.ticket_count }));
                    ticketStatusChartError = null;
                } else {
                    ticketStatusData = [];
                    ticketStatusChartError = t('db.noDataAvailable');
                }
            } catch (error) {
                console.error('Error parsing ticket status count data:', error);
                ticketStatusData = [];
                ticketStatusChartError = t('db.noDataAvailable');
            }
        } else {
            console.error('Failed to fetch ticket status count data:', ticketStatusResult.status === 'rejected' ? ticketStatusResult.reason : `HTTP error: ${ticketStatusResult.value?.status}`);
            ticketStatusData = [];
            ticketStatusChartError = t('db.noDataAvailable');
        }
        isLoadingTicketStatusChart = false;

        // Process Closed Tickets By Case Type Chart
        if (closedTicketsByCaseTypeResult.status === 'fulfilled' && closedTicketsByCaseTypeResult.value.ok) {
            try {
                const data: ClosedTicketsByCaseTypeAPIResponse[] = await closedTicketsByCaseTypeResult.value.json();
                if (Array.isArray(data) && data.length > 0) {
                    closedTicketsByCaseType = data.map(item => ({ caseType: item.case_type, count: item.ticket_count }));
                    caseTypeChartError = null;
                } else {
                    closedTicketsByCaseType = [];
                    caseTypeChartError = t('db.noDataAvailable');
                }
            } catch (error) {
                console.error('Error parsing closed tickets by case type data:', error);
                closedTicketsByCaseType = [];
                caseTypeChartError = t('db.noDataAvailable');
            }
        } else {
            console.error('Failed to fetch closed tickets by case type data:', closedTicketsByCaseTypeResult.status === 'rejected' ? closedTicketsByCaseTypeResult.reason : `HTTP error: ${closedTicketsByCaseTypeResult.value?.status}`);
            closedTicketsByCaseType = [];
            caseTypeChartError = t('db.noDataAvailable');
        }
        isLoadingCaseTypeChart = false;

        // Process Closed Tickets by Sub-Case Type Chart
        if (closedTicketsBySubCaseTypeResult.status === 'fulfilled' && closedTicketsBySubCaseTypeResult.value.ok) {
            try {
                const data: ClosedTicketsBySubCaseTypeAPIResponse[] = await closedTicketsBySubCaseTypeResult.value.json();
                if (Array.isArray(data) && data.length > 0) {
                    closedTicketsBySubCaseType = data.map(item => ({ subCaseType: item.case_topic, count: item.ticket_count }));
                    subCaseTypeChartError = null;
                } else {
                    closedTicketsBySubCaseType = [];
                    subCaseTypeChartError = t('db.noDataAvailable');
                }
            } catch (error) {
                console.error('Error parsing closed tickets by sub-case type data:', error);
                closedTicketsBySubCaseType = [];
                subCaseTypeChartError = t('db.noDataAvailable');
            }
        } else {
            console.error('Failed to fetch closed tickets by sub-case type data:', closedTicketsBySubCaseTypeResult.status === 'rejected' ? closedTicketsBySubCaseTypeResult.reason : `HTTP error: ${closedTicketsBySubCaseTypeResult.value?.status}`);
            closedTicketsBySubCaseType = [];
            subCaseTypeChartError = t('db.noDataAvailable');
        }
        isLoadingSubCaseTypeChart = false;

        // Process Closed Tickets by Case & Sub-Case Type Table
        if (closedTicketsByCaseAndTopicResult.status === 'fulfilled' && closedTicketsByCaseAndTopicResult.value.ok) {
            try {
                const data: ClosedTicketsByCaseTypeAndTopicAPIResponse[] = await closedTicketsByCaseAndTopicResult.value.json();
                if (Array.isArray(data) && data.length > 0) {
                    closedCaseSubCaseTable = applyInitialSort(data.map(item => ({
                        caseType: item.case_type,
                        subCaseType: item.case_topic,
                        count: item.ticket_count
                    })), 'closedCaseSubCaseTable');
                    caseSubCaseTableError = null;
                } else {
                    closedCaseSubCaseTable = [];
                    caseSubCaseTableError = t('db.noDataAvailable');
                }
            } catch (error) {
                console.error('Error parsing closed tickets by case type and topic data:', error);
                closedCaseSubCaseTable = [];
                caseSubCaseTableError = t('db.noDataAvailable');
            }
        } else {
            console.error('Failed to fetch closed tickets by case type and topic data:', closedTicketsByCaseAndTopicResult.status === 'rejected' ? closedTicketsByCaseAndTopicResult.reason : `HTTP error: ${closedTicketsByCaseAndTopicResult.value?.status}`);
            closedCaseSubCaseTable = [];
            caseSubCaseTableError = t('db.noDataAvailable');
        }
        isLoadingCaseSubCaseTable = false;
    }

    // Functions to add for downloading Excel files
    async function downloadAgentTicketsByStatusExcel() {
        console.log("Attempting to download agent tickets by status Excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = new URLSearchParams();

        if (startDate) urlParams.append('start_date', startDate);
        if (endDate) urlParams.append('end_date', endDate);

        // Append language param only if Thai
        if (currentLang === 'th') {
            urlParams.append('lang', 'th');
        }

        const query = urlParams.toString();
        const downloadUrl = `${getBackendUrl()}/dashboard/api/ticket-status-count.xlsx/?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${t('dbChatPerformance.agentTicketsByStatusExcel')}_${startDate}_${endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(t('db.excelAlert'));
        }
    }

    async function downloadAgentClosedTicketsByCaseTypeExcel() {
        console.log("Attempting to download agent closed tickets by case type excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = new URLSearchParams();

        if (startDate) urlParams.append('start_date', startDate);
        if (endDate) urlParams.append('end_date', endDate);

        // Append language param only if Thai
        if (currentLang === 'th') {
            urlParams.append('lang', 'th');
        }

        const query = urlParams.toString();
        const downloadUrl = `${getBackendUrl()}/dashboard/api/closed-tickets-by-case-type.xlsx/?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${t('dbChatPerformance.agentClosedTicketsByCaseTypeExcel')}_${startDate}_${endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(t('db.excelAlert'));
        }
    }


    async function downloadAgentClosedTicketsBySubCaseTypeExcel() {
        console.log("Attempting to download agent closed tickets by sub-case type excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = new URLSearchParams();

        if (startDate) urlParams.append('start_date', startDate);
        if (endDate) urlParams.append('end_date', endDate);

        // Append language param only if Thai
        if (currentLang === 'th') {
            urlParams.append('lang', 'th');
        }

        const query = urlParams.toString();
        const downloadUrl = `${getBackendUrl()}/dashboard/api/closed-tickets-by-case-topic.xlsx/?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${t('dbChatPerformance.agentClosedTicketsBySubCaseTypeExcel')}_${startDate}_${endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(t('db.excelAlert'));
        }
    }

    async function downloadAgentClosedTicketsByCaseAndSubCaseTypeExcel() {
        console.log("Attempting to download agent closed tickets by case and sub-case type excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = new URLSearchParams();

        if (startDate) urlParams.append('start_date', startDate);
        if (endDate) urlParams.append('end_date', endDate);

        // Append language param only if Thai
        if (currentLang === 'th') {
            urlParams.append('lang', 'th');
        }

        const query = urlParams.toString();
        const downloadUrl = `${getBackendUrl()}/dashboard/api/closed-tickets-by-case-type-and-topic.xlsx/?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${t('dbChatPerformance.agentClosedTicketsCaseAndSubCaseExcel')}_${startDate}_${endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(t('db.excelAlert'));
        }
    } 
     
    onMount(() => {
        console.log("Component mounted: initiating fetchData");
        fetchData();
    });

    $: startDate, endDate, $currentLanguage, fetchData();

</script>

{#if isTicketStatusChartExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbChatPerformance.agentTicketsByStatus')}</h3>
                <button on:click={() => isTicketStatusChartExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingTicketStatusChart}
                    <div class="flex flex-col items-center justify-center h-full text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if ticketStatusChartError}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else if ticketStatusData.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <BarChart
                        data={ticketStatusData}
                        chartType="horizontalBar"
                        labelKey="status"
                        valueKey="amount"
                        label=""
                        barColor={COLORS.blue}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isCaseTypeChartExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbChatPerformance.agentClosedTicketsByCaseType')}</h3>
                <button on:click={() => isCaseTypeChartExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingCaseTypeChart}
                    <div class="flex flex-col items-center justify-center h-full text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if caseTypeChartError}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else if closedTicketsByCaseType.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <BarChart
                        data={closedTicketsByCaseType} chartType="horizontalBar"
                        labelKey="caseType" valueKey="count" label=""
                        barColor={COLORS.blue}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isSubCaseTypeChartExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbChatPerformance.agentClosedTicketsBySubCaseType')}</h3>
                <button on:click={() => isSubCaseTypeChartExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingSubCaseTypeChart}
                    <div class="flex flex-col items-center justify-center h-full text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if subCaseTypeChartError}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else if closedTicketsBySubCaseType.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <BarChart
                        data={closedTicketsBySubCaseType}
                        chartType="horizontalBar"
                        labelKey="subCaseType"
                        valueKey="count"
                        label=""
                        barColor={COLORS.blue}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isCaseSubCaseTableExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbChatPerformance.agentClosedTicketsCaseAndSubCase')}</h3>
                <button on:click={() => isCaseSubCaseTableExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full overflow-y-auto">
                {#if isLoadingCaseSubCaseTable}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-full">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if caseSubCaseTableError}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else if closedCaseSubCaseTable.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => closedCaseSubCaseTable = sortTable(closedCaseSubCaseTable, 'caseType', 'closedCaseSubCaseTable')}>
                                    {t('dbChatPerformance.caseType')}
                                    {#if currentSort.closedCaseSubCaseTable.column === 'caseType'}
                                        {#if currentSort.closedCaseSubCaseTable.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => closedCaseSubCaseTable = sortTable(closedCaseSubCaseTable, 'subCaseType', 'closedCaseSubCaseTable')}>
                                    {t('dbChatPerformance.subCaseType')}
                                    {#if currentSort.closedCaseSubCaseTable.column === 'subCaseType'}
                                        {#if currentSort.closedCaseSubCaseTable.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => closedCaseSubCaseTable = sortTable(closedCaseSubCaseTable, 'count', 'closedCaseSubCaseTable')}>
                                    {t('dbChatPerformance.count')}
                                    {#if currentSort.closedCaseSubCaseTable.column === 'count'}
                                        {#if currentSort.closedCaseSubCaseTable.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each closedCaseSubCaseTable as item (item.caseType + item.subCaseType)}
                                <tr>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{item.caseType}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.subCaseType}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.count}</td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
    </div>
{/if}

<div class="flex flex-col gap-2">
    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-2">
        <div class="lg:col-span-2 grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-2">
            <div class="bg-white rounded-lg shadow-md p-5">
                <ScoreCard
                    title={t('dbChatPerformance.totalIncomingMessages')}
                    value={chatVolume}
                    valueColor="text-black-600"
                    trendValue={chatVolumeTrend} trendUnit="%"
                />
            </div>
            <div class="bg-white rounded-lg shadow-md p-5">
                <ScoreCard
                    title={t('dbChatPerformance.totalAgentTickets')}
                    value={allTickets}
                    valueColor="text-black-600"
                    trendValue={allTicketsTrend} trendUnit="%"
                />
            </div>
            <div class="bg-white rounded-lg shadow-md p-5">
                <ScoreCard
                    title={t('dbChatPerformance.totalAgentClosedTickets')}
                    value={allClosedTickets}
                    valueColor="text-black-600"
                    trendValue={allClosedTicketsTrend} trendUnit="%"
                />
            </div>
            <div class="bg-white rounded-lg shadow-md p-5">
                <ScoreCard
                    title={t('dbChatPerformance.agentTicketClosureRateVsIncoming')}
                    value={closedRate}
                    valueColor="text-black-600"
                    trendValue={closedRateTrend} trendUnit="%"
                />
            </div>
            <div class="bg-white rounded-lg shadow-md p-5">
                <ScoreCard
                    title={t('dbChatPerformance.averageAgentResponseTimeSeconds')}
                    value={avgResponseTime}
                    valueColor="text-black-600"
                    trendValue={responseTimeTrend} trendUnit="%" isTrendPositiveIsGood={false}
                />
            </div>
            <div class="bg-white rounded-lg shadow-md p-5">
                <ScoreCard
                    title={t('dbChatPerformance.agentResponseRateWithin6Seconds')}
                    value={responseRateWithin6s}
                    valueColor="text-black-600"
                    trendValue={responseRateWithin6sTrend} trendUnit="%"
                />
            </div>
            <div class="bg-white rounded-lg shadow-md p-5">
                <ScoreCard
                    title={t('dbChatPerformance.averageAgentHandlingTimeMinutes')}
                    value={avgHandlingTime}
                    valueColor="text-black-600"
                    trendValue={handlingTimeRate} trendUnit="%" isTrendPositiveIsGood={false}
                />
            </div>
            <div class="bg-white rounded-lg shadow-md p-5">
                <ScoreCard
                    title={t('dbChatPerformance.agentHandlingRateWithin5Minutes')}
                    value={handlingRateWithin5mins}
                    valueColor="text-black-600"
                    trendValue={handlingRateWithin5minsTrend} trendUnit="%"
                />
            </div>
        </div>
        <div class="lg:col-span-1 bg-white p-5 rounded-lg shadow-md">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbChatPerformance.agentTicketsByStatus')}</h2>
                <div class="flex gap-2 flex-wrap sm:flex-nowrap">
                    <button 
                        on:click={() => isTicketStatusChartExpanded = true}
                        class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <!-- {t('db.expand')} -->
                        <ExpandOutline class="h-5 w-5" />
                    </button>
                    <button 
                        on:click={downloadAgentTicketsByStatusExcel}
                        class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <!-- {t('db.downloadExcel')} -->
                        <DownloadOutline class="h-5 w-5" />
                    </button>
                </div>
            </div>
            <div class="w-full h-[24rem] sm:h-[24rem] lg:h-[24rem]">
                {#if isLoadingTicketStatusChart}
                    <div class="flex flex-col items-center justify-center h-full text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if ticketStatusChartError}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else if ticketStatusData.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <BarChart
                        data={ticketStatusData}
                        chartType="horizontalBar"
                        labelKey="status"
                        valueKey="amount"
                        label=""
                        barColor={COLORS.blue}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-2">
        <div class="bg-white p-6 rounded-lg shadow-md flex flex-col">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbChatPerformance.agentClosedTicketsByCaseType')}</h2>
                <div class="flex gap-2 flex-wrap sm:flex-nowrap">
                    <button 
                        on:click={() => isCaseTypeChartExpanded = true}
                        class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <!-- {t('db.expand')} -->
                        <ExpandOutline class="h-5 w-5" />
                    </button>
                    <button 
                        on:click={downloadAgentClosedTicketsByCaseTypeExcel}
                        class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <!-- {t('db.downloadExcel')} -->
                        <DownloadOutline class="h-5 w-5" />
                    </button>
                </div>
            </div>
            <div class="w-full h-[26rem] sm:h-[26rem] lg:h-[26rem]">
                {#if isLoadingCaseTypeChart}
                    <div class="flex flex-col items-center justify-center h-full text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if caseTypeChartError}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else if closedTicketsByCaseType.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <BarChart
                        data={closedTicketsByCaseType}
                        chartType="horizontalBar"
                        labelKey="caseType"
                        valueKey="count"
                        label=""
                        barColor={COLORS.blue}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-md flex flex-col">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbChatPerformance.agentClosedTicketsBySubCaseType')}</h2>
                <div class="flex gap-2 flex-wrap sm:flex-nowrap">
                    <button 
                        on:click={() => isSubCaseTypeChartExpanded = true}
                        class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <!-- {t('db.expand')} -->
                        <ExpandOutline class="h-5 w-5" />
                    </button>
                    <button 
                        on:click={downloadAgentClosedTicketsBySubCaseTypeExcel}
                        class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <!-- {t('db.downloadExcel')} -->
                        <DownloadOutline class="h-5 w-5" />
                    </button>
                </div>
            </div>
            <div class="w-full h-[26rem] sm:h-[26rem] lg:h-[26rem]">
                {#if isLoadingSubCaseTypeChart}
                    <div class="flex flex-col items-center justify-center h-full text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if subCaseTypeChartError}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else if closedTicketsBySubCaseType.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <BarChart
                        data={closedTicketsBySubCaseType}
                        chartType="horizontalBar"
                        labelKey="subCaseType"
                        valueKey="count"
                        label=""
                        barColor={COLORS.blue}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-md overflow-y-auto">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbChatPerformance.agentClosedTicketsCaseAndSubCase')}</h2>
                <div class="flex gap-2 flex-wrap sm:flex-nowrap">
                    <button 
                        on:click={() => isCaseSubCaseTableExpanded = true}
                        class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <!-- {t('db.expand')} -->
                        <ExpandOutline class="h-5 w-5" />
                    </button>
                    <button 
                        on:click={downloadAgentClosedTicketsByCaseAndSubCaseTypeExcel}
                        class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <!-- {t('db.downloadExcel')} -->
                        <DownloadOutline class="h-5 w-5" />
                    </button>
                </div>
            </div>
            <div class="overflow-y-auto max-h-96">
                {#if isLoadingCaseSubCaseTable}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-48">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if caseSubCaseTableError}
                    <div class="text-gray-600 text-center text-lg h-48 flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else if closedCaseSubCaseTable.length === 0}
                    <div class="text-gray-600 text-center text-lg h-48 flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => closedCaseSubCaseTable = sortTable(closedCaseSubCaseTable, 'caseType', 'closedCaseSubCaseTable')}>
                                    {t('dbChatPerformance.caseType')}
                                    {#if currentSort.closedCaseSubCaseTable.column === 'caseType'}
                                        {#if currentSort.closedCaseSubCaseTable.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => closedCaseSubCaseTable = sortTable(closedCaseSubCaseTable, 'subCaseType', 'closedCaseSubCaseTable')}>
                                    {t('dbChatPerformance.subCaseType')}
                                    {#if currentSort.closedCaseSubCaseTable.column === 'subCaseType'}
                                        {#if currentSort.closedCaseSubCaseTable.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => closedCaseSubCaseTable = sortTable(closedCaseSubCaseTable, 'count', 'closedCaseSubCaseTable')}>
                                    {t('dbChatPerformance.count')}
                                    {#if currentSort.closedCaseSubCaseTable.column === 'count'}
                                        {#if currentSort.closedCaseSubCaseTable.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each closedCaseSubCaseTable as item (item.caseType + item.subCaseType)}
                                <tr>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{item.caseType}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.subCaseType}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.count}</td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
    </div>
</div>