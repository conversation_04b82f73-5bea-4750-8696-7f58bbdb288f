<!-- $/src/routes/(site)/settings/account/+page.svelte -->
<script lang="ts">
    import { t } from '$lib/stores/i18n';

    import type { PageData } from './$types';
    import { onMount } from 'svelte';

    import {
      UserCircleSolid,
      AdjustmentsVerticalSolid,
      ClockSolid,
      CheckCircleSolid
    } from 'flowbite-svelte-icons';
    // import { Breadcrumb, BreadcrumbItem, Toast } from 'flowbite-svelte';
    import { fly } from 'svelte/transition';

    import UserProfile from '$src/lib/components/settings/account/UserProfile.svelte';
    import UserAssign from '$src/lib/components/settings/account/UserAssign.svelte';
    import WorkshiftSection from '$src/lib/components/settings/account/WorkshiftSection.svelte';
    // import UserTag     from '$src/lib/components/settings/account/UserTag.svelte';
    // import UserDepartment from '$src/lib/components/settings/account/UserDepartment.svelte';
    // import UserPartner from '$src/lib/components/settings/account/UserPartner.svelte';

    export let data: PageData;
    $: ({ system_setting, user, all_partners, all_departments, all_tags, user_schedule, business_hours } = data);
    $: dominantColor = system_setting?.DOMINANT_COLOR || '#4B5563';
  
    let activeTab: 'profile' | 'schedule' = 'profile';
  
    function handleTabChange(tab: typeof activeTab) {
      activeTab = tab;
    }
  
    // Inject dynamic CSS for hover & active states
    onMount(() => {
      const styleId = 'dynamic-tab-styles';
      let styleEl = document.getElementById(styleId) as HTMLStyleElement;
      if (!styleEl) {
        styleEl = document.createElement('style');
        styleEl.id = styleId;
        document.head.appendChild(styleEl);
      }
      styleEl.textContent = `
        .custom-tabs [role="tab"][aria-selected="true"] {
          border-bottom: 2px solid ${dominantColor} !important;
          color: ${dominantColor} !important;
        }
        .custom-tabs [role="tab"]:hover {
          color: ${dominantColor} !important;
        }
      `;
    });

    let pageTitle = "Account";
    
    // Toast state for feedback
    let toastMessage = 'Successfully';
    let toastStatus = false;
    let counter = 2;
    
    // Function to handle settings updates
    function handleSettingsUpdated(event) {
        // Show toast notification
        toastMessage = event.detail?.message || "Account updated successfully!";
        toastStatus = true;
        counter = 2;
        timeout();
        // window.location.reload(); // Uncommented this line to reload the page
    }
    
    // Toast timeout method
    function timeout() {
        if (counter > 0 && toastStatus) {
            setTimeout(() => {
                counter--;
                timeout();
            }, 1000);
        } else {
            toastStatus = false;
        }
    }
    
    // Initialize event listener
    onMount(() => {
        window.addEventListener('settings-updated', handleSettingsUpdated);
        
        return () => {
            window.removeEventListener('settings-updated', handleSettingsUpdated);
        };
    });
</script>
  
<svelte:head>
    <title>{t('your_account')}</title>
</svelte:head>

{#if toastStatus}
    <Toast
        id="settings-account-toast"
        color="green"
        transition={fly}
        params={{ x: 200 }}
        bind:toastStatus
        class="fixed left-3/4 top-1/4 -translate-x-1/2 -translate-y-1/2 transform"
    >
        <CheckCircleSolid slot="icon" class="h-5 w-5" />
        {toastMessage}
    </Toast>
{/if}

<div class="min-h-screen rounded-lg bg-white">
  <div id="settings-account-container" class="mx-auto max-w-7xl px-4 py-10 sm:px-6 lg:px-8">
      <!-- <Breadcrumb id="settings-account-breadcrumb" aria-label="Default breadcrumb example" class="mb-3">
          <BreadcrumbItem href="/" home>
              <span class="text-gray-400">{t('home')}</span>
          </BreadcrumbItem>
          <BreadcrumbItem>
              <span class="text-gray-400">{t('settings')}</span>
          </BreadcrumbItem>
          <BreadcrumbItem>
              <span class="text-gray-700">{t('your_account')}</span>
          </BreadcrumbItem>
      </Breadcrumb> -->

      <div id="settings-account-header" class="mb-6">
          <h2 id="settings-account-title" class="text-2xl font-bold">{t('account_settings')}</h2>
          <p id="settings-account-description" class="text-gray-600">{t('account_settings_description')}</p>
      </div>
    
      <!-- Custom Tabs -->
      <div id="settings-account-tabs-container" class="flex border-b custom-tabs">
          <button
            id="settings-account-tab-profile"
            role="tab"
            aria-selected={activeTab === 'profile'}
            class="px-6 py-4 text-sm font-medium border-b-2"
            style={activeTab === 'profile'
              ? `color: ${dominantColor}; border-color: ${dominantColor};`
              : ''}
            on:click={() => handleTabChange('profile')}
          >
            <div class="flex items-center gap-2">
              <!-- <UserCircleSolid size="md" color={activeTab === 'profile' ? dominantColor : undefined} /> -->
              {t('tab_profile')}
            </div>
          </button>
    
          <!-- <button
            role="tab"
            aria-selected={activeTab === 'tags'}
            class="px-6 py-4 text-sm font-medium border-b-2"
            style={activeTab === 'tags'
              ? `color: ${dominantColor}; border-color: ${dominantColor};`
              : ''}
            on:click={() => handleTabChange('tags')}
          >
            <div class="flex items-center gap-2">
              <AdjustmentsVerticalSolid size="md" color={activeTab === 'tags' ? dominantColor : undefined} />
              Tags
            </div>
          </button> -->
    
          <button
            id="settings-account-tab-schedule"
            role="tab"
            aria-selected={activeTab === 'schedule'}
            class="px-6 py-4 text-sm font-medium border-b-2"
            style={activeTab === 'schedule'
              ? `color: ${dominantColor}; border-color: ${dominantColor};`
              : ''}
            on:click={() => handleTabChange('schedule')}
          >
            <div class="flex items-center gap-2">
              <!-- <ClockSolid size="md" color={activeTab === 'schedule' ? dominantColor : undefined} /> -->
              {t('tab_schedule')}
            </div>
          </button>
      </div>
    
      
      <!-- Tab Content -->
      <div id="settings-account-tab-content"> 
          {#if activeTab === 'profile'}
              <div id="settings-account-profile-content">
                  <UserProfile {user} on:settings-updated={handleSettingsUpdated} />
                  <UserAssign 
                      partners={user.partners ?? []} 
                      departments={user.departments ?? []} 
                      tags={user.user_tags ?? []} 
                      all_tags={all_tags}
                      all_partners={all_partners}
                      all_departments={all_departments}
                  />
              </div>
          {/if}

          <!--
              <UserPartner    {user} {partners} />
              <UserDepartment {user} {departments} />
              <UserTag        {user} {tags} /> 
          -->
    
          {#if activeTab === 'schedule'}
              <div id="settings-account-schedule-content">
                  <WorkshiftSection 
                      {user_schedule} 
                      {business_hours}
                  />
              </div>
          {/if}

          <!-- on:settings-updated={handleSettingsUpdated}  -->
      </div>
  </div>
</div>