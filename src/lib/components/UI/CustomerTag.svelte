<!-- CustomerTag.svelte -->
<script lang="ts">
	import { t } from '$src/lib/stores/i18n';
	import { enhance } from '$app/forms';
	import { Indicator, Button, Modal, Alert, Checkbox, Label } from 'flowbite-svelte';
	import { EditOutline, CheckOutline } from 'flowbite-svelte-icons';
	import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';
	import { colorOptions, getColorClass } from '$lib/utils'; // adjust the path if needed

	// Expecting the current customer and the list of customer_tags as props.
	export let customer: any;
	export let customer_tags: any[];
	export let onRefresh: (() => Promise<void>) | undefined = undefined;
	// console.log('CustomerTag component initialized with customer:', customer);

	let customerAssignTagForm: HTMLFormElement;
	let customerAssignTagModalOpen = false;
	let currentcustomer: any = null;
	let selectedTagIds: (string | number)[] = [];
	let initialSelectedTagIds: (string | number)[] = [];

	let showSuccessMessage = false;
	let showErrorMessage = false;
	let successMessage = '';
	let errorMessage = '';
	let fieldErrors: Record<string, string[]> = {}; // Multi-field error support

	// Function to parse error messages (supports both single string and multi-field errors)
	function parseErrorMessages(error: any): {
		fieldErrors: Record<string, string[]>;
		generalError: string | null;
	} {
		if (!error) return { fieldErrors: {}, generalError: null };

		let errorObj = error;

		// Handle string errors - check if it's a JSON string first
		if (typeof error === 'string') {
			// Try to parse as JSON first
			try {
				errorObj = JSON.parse(error);
			} catch (e) {
				// If JSON parsing fails, treat as plain string (backward compatibility)
				return { fieldErrors: {}, generalError: error };
			}
		}

		// Handle object errors with field-specific messages
		if (typeof errorObj === 'object' && errorObj !== null) {
			const fieldErrors: Record<string, string[]> = {};
			let hasFieldErrors = false;

			for (const [fieldName, fieldErrorArray] of Object.entries(errorObj)) {
				if (Array.isArray(fieldErrorArray)) {
					const validErrors = fieldErrorArray.filter((msg: any) => typeof msg === 'string');
					if (validErrors.length > 0) {
						fieldErrors[fieldName] = validErrors;
						hasFieldErrors = true;
					}
				}
			}

			if (hasFieldErrors) {
				return { fieldErrors, generalError: null };
			}
		}

		return { fieldErrors: {}, generalError: 'An error occurred' };
	}

	// Function to dismiss alerts when checkbox selections change
	function dismissAlerts() {
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
		fieldErrors = {};
	}

	// Open the modal and initialize with the current customer's tags if available.
	function opencustomerAssignTagModal(customer: any) {
		currentcustomer = { ...customer };

		if (currentcustomer.tags && Array.isArray(currentcustomer.tags)) {
			selectedTagIds = currentcustomer.tags
				.map((tag: any) => (typeof tag === 'object' ? tag.id : tag))
				.filter((id: any) => id !== undefined && id !== null && !isNaN(Number(id)));
		} else {
			selectedTagIds = [];
		}

		// Store initial state for change detection
		initialSelectedTagIds = [...selectedTagIds];

		customerAssignTagModalOpen = true;
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
		fieldErrors = {}; // Reset field errors
	}

	function handlecustomerAssigncustomer_tagsubmit() {
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
		fieldErrors = {}; // Reset field errors
	}

	// Reactive statement for change detection
	$: hasChanges =
		JSON.stringify([...selectedTagIds].sort()) !==
		JSON.stringify([...initialSelectedTagIds].sort());

	$: enhanceOptions = {
		modalOpen: customerAssignTagModalOpen,
		setModalOpen: (value: boolean) => (customerAssignTagModalOpen = value),
		setShowSuccessMessage: (value: boolean) => (showSuccessMessage = value),
		setSuccessMessage: (value: string) => (successMessage = value),
		setShowErrorMessage: (value: boolean) => (showErrorMessage = value),
		setErrorMessage: (value: string) => {
			const parsedResult = parseErrorMessages(value);
			fieldErrors = parsedResult.fieldErrors;
			errorMessage = parsedResult.generalError || '';
			// Set showErrorMessage to true when we have either field errors or general error
			showErrorMessage =
				Object.keys(parsedResult.fieldErrors).length > 0 || parsedResult.generalError !== null;
		},
		onSuccess: async () => {
			// Update initial state to match current selections
			initialSelectedTagIds = [...selectedTagIds];

			// Close the modal
			customerAssignTagModalOpen = false;

			// Call the refresh function if provided
			if (onRefresh) {
				await onRefresh();
			}
		}
	};

	$: tagOptions =
		customer_tags?.map((tag) => ({
			value: tag.id,
			name: `${tag.name}`,
			color: `${tag.color}`
		})) || [];

	// Debug logging
	// $: {
	//     console.log('CustomerTag component debug:');
	//     console.log('- customer_tags prop:', customer_tags);
	//     console.log('- tagOptions computed:', tagOptions);
	//     console.log('- tagOptions length:', tagOptions.length);
	// }
</script>

<!-- Button to open the assign tag modal -->
<Button id="edit-tag-button" size="xs" type="button" color="blue" on:click={() => opencustomerAssignTagModal(customer)} data-testid="edit-tag-button">
	<EditOutline class="mr-2 h-4 w-4" />
	{t('edit_tag')}
</Button>

<!-- Modal for assigning customer_tags -->
<Modal bind:open={customerAssignTagModalOpen} size="md" autoclose={false} class="w-full">
	<h2 slot="header">{t('assign_customer_tags')}</h2>
	{#if currentcustomer}
		{#if showSuccessMessage}
			<Alert color="green" class="mb-4">
				{successMessage}
			</Alert>
		{/if}
		{#if showErrorMessage}
			<Alert color="red" class="mb-4">
				{errorMessage}
			</Alert>
		{/if}
		<!-- Field-specific error display for multi-field errors -->
		{#if Object.keys(fieldErrors).length > 0}
			{#each Object.entries(fieldErrors) as [fieldName, errors]}
				{#each errors as error}
					<Alert color="red" class="mb-4">
						<strong>{fieldName}:</strong>
						{error}
					</Alert>
				{/each}
			{/each}
		{/if}
		<form
			bind:this={customerAssignTagForm}
			action="?/assign_customer_tag"
			method="POST"
			use:enhance={() => handleEnhance(enhanceOptions)}
			on:submit={handlecustomerAssigncustomer_tagsubmit}
		>
			<input type="hidden" name="customer_id" value={customer.customer_id} />
			<div class="min-h-[200px]">
				<!-- <label for="Selectedcustomer_tags" class="mb-1 block text-left text-sm font-medium text-gray-700">
					{t('select_customer_tags')}
				</label> -->
				{#if tagOptions.length > 0}
					{#each tagOptions as tag (tag.value)}
						<div>
							<Label class="flex items-center text-sm font-medium text-gray-700">
								<Checkbox
									checked={selectedTagIds.includes(tag.value)}
									value={tag.value}
									on:change={() => {
										if (selectedTagIds.includes(tag.value)) {
											selectedTagIds = selectedTagIds.filter((id) => id !== tag.value);
										} else {
											selectedTagIds = [...selectedTagIds, tag.value];
										}
										dismissAlerts(); // Dismiss alerts when checkbox selection changes
									}}
									class="text-gray-700 focus:ring-gray-700 p-2"
									inline
								/>
								<Indicator size="lg" class={`${getColorClass(tag.color)} ml-1 mr-2`} />
								{tag.name}
							</Label>
						</div>
					{/each}
				{:else}
					<div class="py-4 text-sm text-gray-500">
						{t('no_tags_available')}
					</div>
				{/if}
				<input type="hidden" name="tag_ids[]" value={selectedTagIds} />
			</div>
		</form>
	{/if}
	<svelte:fragment slot="footer">
		<Button color="green" disabled={!hasChanges} on:click={() => customerAssignTagForm.requestSubmit()}
			><CheckOutline class="mr-2 h-4 w-4" />{t('save')}</Button
		>
		<Button color="light" on:click={() => (customerAssignTagModalOpen = false)}>{t('cancel')}</Button>
	</svelte:fragment>
</Modal>
