<script lang="ts">
	import { enhance } from '$app/forms';
	import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ert, Checkbox, Label, Indicator } from 'flowbite-svelte';
	import { CheckOutline } from 'flowbite-svelte-icons';
	import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';
	import { t } from '$src/lib/stores/i18n';
	import { getColorClass } from '$lib/utils';

	// Expecting the current user and the list of tags as props.
	export let user: any;
	export let tags: any[];
	export let onSuccess: () => void = () => {}; // Callback for successful operations

	let userAssignTagForm: HTMLFormElement;
	let userAssignTagModalOpen = false;
	let currentUser: any = null;
	let selectedTagIds: (string | number)[] = [];
	let initialSelectedTagIds: (string | number)[] = []; // Track initial state for change detection

	let showSuccessMessage = false;
	let showErrorMessage = false;
	let successMessage = '';
	let errorMessage = '';
	let fieldErrors: Record<string, string[]> = {}; // Multi-field error support
	let isSubmitting = false;

	// Function to parse error messages (supports both single string and multi-field errors)
	function parseErrorMessages(error: any): {
		fieldErrors: Record<string, string[]>;
		generalError: string | null;
	} {
		if (!error) return { fieldErrors: {}, generalError: null };

		let errorObj = error;

		// Handle string errors - check if it's a JSON string first
		if (typeof error === 'string') {
			// Try to parse as JSON first
			try {
				errorObj = JSON.parse(error);
			} catch (e) {
				// If JSON parsing fails, treat as plain string (backward compatibility)
				return { fieldErrors: {}, generalError: error };
			}
		}

		// Handle object errors with field-specific messages
		if (typeof errorObj === 'object' && errorObj !== null) {
			const fieldErrors: Record<string, string[]> = {};
			let hasFieldErrors = false;

			for (const [fieldName, fieldErrorArray] of Object.entries(errorObj)) {
				if (Array.isArray(fieldErrorArray)) {
					const validErrors = fieldErrorArray.filter((msg: any) => typeof msg === 'string');
					if (validErrors.length > 0) {
						fieldErrors[fieldName] = validErrors;
						hasFieldErrors = true;
					}
				}
			}

			if (hasFieldErrors) {
				return { fieldErrors, generalError: null };
			}
		}

		return { fieldErrors: {}, generalError: 'An error occurred' };
	}

	// Function to dismiss alerts when checkbox selections change
	function dismissAlerts() {
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
		fieldErrors = {};
	}

	// Reactive statement to detect changes from initial state
	$: hasChanges =
		JSON.stringify([...selectedTagIds].sort()) !==
		JSON.stringify([...initialSelectedTagIds].sort());

	// Open the modal and initialize with the current user's tags if available.
	function openUserAssignTagModal(user: any) {
		currentUser = { ...user };

		if (currentUser.tags && Array.isArray(currentUser.tags)) {
			// Match currentUser.tags with the main tags array
			// First try to use id if available, otherwise match by name as fallback
			selectedTagIds = currentUser.tags
				.map((userTag: any) => {
					// If the tag object has an id, use it directly
					if (userTag.id !== undefined && userTag.id !== null) {
						return userTag.id;
					}
					// Otherwise, find the matching tag in the main tags array by name
					const matchingTag = tags.find((tag: any) => tag.name === userTag.name);
					return matchingTag ? matchingTag.id : null;
				})
				// Only keep valid IDs (remove null values)
				.filter((id: any) => id !== null && id !== undefined && !isNaN(Number(id)));
		} else {
			selectedTagIds = [];
		}

		// Store initial state for change detection
		initialSelectedTagIds = [...selectedTagIds];

		userAssignTagModalOpen = true;
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
		fieldErrors = {}; // Reset field errors
		isSubmitting = false;
	}

	function handleUserAssignTagSubmit(_event: Event) {
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
		isSubmitting = true;
	}

	$: enhanceOptions = {
		modalOpen: userAssignTagModalOpen,
		setModalOpen: (value: boolean) => (userAssignTagModalOpen = value),
		setShowSuccessMessage: (value: boolean) => (showSuccessMessage = value),
		setSuccessMessage: (value: string) => (successMessage = value),
		successMessage: t('user_assign_tag_success'),
		setShowErrorMessage: (value: boolean) => (showErrorMessage = value),
		setErrorMessage: (value: string) => {
			const parsedResult = parseErrorMessages(value);
			fieldErrors = parsedResult.fieldErrors;
			errorMessage = parsedResult.generalError || '';
			// Set showErrorMessage to true when we have either field errors or general error
			showErrorMessage =
				Object.keys(parsedResult.fieldErrors).length > 0 || parsedResult.generalError !== null;
		},
		// Enhanced success behavior - close modal and show toast
		useToastOnSuccess: true,
		closeModalOnSuccess: true,
		onSuccess: () => {
			// Call the original onSuccess if provided
			if (onSuccess) onSuccess();
			isSubmitting = false;
		},
		onError: () => {
			isSubmitting = false;
		}
	};

	$: tagOptions = tags.map((tag) => ({
		value: tag.id,
		name: `${tag.name}`,
		color: `${tag.color}`
	}));
</script>

<!-- Button to open the assign tag modal -->
<Button
	color="none"
	class="w-full justify-start p-2 text-left hover:bg-gray-100"
	on:click={() => openUserAssignTagModal(user)}
>
	{t('user_assign_tag')}
</Button>

<!-- Modal for assigning tags -->
<Modal bind:open={userAssignTagModalOpen} size="md" autoclose={false} class="w-full">
	<h2 slot="header">{t('user_assign_tag')}</h2>
	{#if currentUser}
		{#if showSuccessMessage}
			<Alert color="green" class="mb-4">
				{successMessage}
			</Alert>
		{/if}
		{#if showErrorMessage}
			<Alert color="red" class="mb-4">
				{errorMessage}
			</Alert>
		{/if}
		<!-- Field-specific error display for multi-field errors -->
		{#if Object.keys(fieldErrors).length > 0}
			{#each Object.entries(fieldErrors) as [fieldName, errors]}
				{#each errors as error}
					<Alert color="red" class="mb-4">
						<strong>{fieldName}:</strong>
						{error}
					</Alert>
				{/each}
			{/each}
		{/if}
		<form
			bind:this={userAssignTagForm}
			action="?/assign_user_tag"
			method="POST"
			use:enhance={() => handleEnhance(enhanceOptions)}
			on:submit={handleUserAssignTagSubmit}
		>
			<input type="hidden" name="id" value={currentUser.id} />
			<div class="min-h-[200px]">
				<!-- <label for="SelectedTags" class="mb-1 block text-left text-sm font-medium text-gray-700">
					Select User's Tags
				</label> -->
				{#each tagOptions as tag (tag.value)}
					<div>
						<Label class="flex items-center text-sm font-medium text-gray-700">
							<Checkbox
								checked={selectedTagIds.includes(tag.value)}
								value={tag.value}
								on:change={() => {
									if (selectedTagIds.includes(tag.value)) {
										selectedTagIds = selectedTagIds.filter((id) => id !== tag.value);
									} else {
										selectedTagIds = [...selectedTagIds, tag.value];
									}
									dismissAlerts(); // Dismiss alerts when checkbox selection changes
								}}
								class="text-gray-700 focus:ring-gray-700 p-2"
								inline
							/>
							<Indicator size="lg" class={`${getColorClass(tag.color)} ml-1 mr-2`} />
							{tag.name}
						</Label>
					</div>
				{/each}
				<input type="hidden" name="tag_ids[]" value={selectedTagIds} />
			</div>
		</form>
	{/if}
	<svelte:fragment slot="footer">
		<Button 
			color="green" 
			disabled={!hasChanges || isSubmitting} 
			on:click={() => userAssignTagForm.requestSubmit()}
		>
			{#if isSubmitting}
				<span class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2" />
				{t('saving')}
			{:else}
				<CheckOutline class="mr-2 h-4 w-4" />{t('save')}
			{/if}
		</Button>
		<Button color="light" on:click={() => (userAssignTagModalOpen = false)}>{t('cancel')}</Button>
	</svelte:fragment>
</Modal>
