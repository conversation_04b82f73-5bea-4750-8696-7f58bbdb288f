<!-- ConversationView.svelte -->
<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import ConversationHeader from './ConversationHeader.svelte';
	import MessageList from './MessageList_MonitoringDetail.svelte';
	import MessageInput from './MessageInput.svelte';
	import { conversationStore } from '$lib/stores/conversationStore';
	import { conversationService } from '$lib/services/conversationService';
	import { platformWebSocket } from '$lib/websocket/platformWebSocket';
	import type { Message } from '$lib/types/customer';
	import { getBackendUrl } from '$src/lib/config';

	export let customerId: number;
	export let platformId: number;
	export let ticketId: number | null = null;
	export let ticketIds: number[] = []; // New prop for multiple ticket IDs
	export let users: any[] = [];
	export let priorities: any[] = [];
	export let statuses: any[] = [];
	export let topics: any[] = [];
	export let access_token: string = '';
	export let latest_ticket_owner_id: number;
	export let currentLoginUser: any; // Changed from any[] to any
	export let focusedTicketId: number | null = null;
	export let showMessageInput: boolean = true; // Default to true for backward compatibility

	let messages: Message[] = [];
	let loading = true;
	let connected = false;
	let customerName = '';
	let channelName = '';

	let loadingMore = false;
	let hasMore = false;
	let error: string | null = null;

	// Subscribe to conversation store
	$: platformData = $conversationStore;
	$: messages = platformData.messages.get(platformId) || [];
	$: hasMore = platformData.hasMore.get(platformId) || false;
	$: loadingMore = platformData.loadingStates.get(platformId) || false;

	// React to platformId, customerId, or ticketIds changes
	$: if (platformId && customerId && ticketIds) {
		console.log('Reactive statement triggered with ticketIds:', ticketIds);
		loadConversationForPlatform(customerId, platformId, ticketIds);
	}

	async function loadConversationForPlatform(custId: number, platId: number, currentTicketIds: number[] = []) {
		// Reset error state
		error = null;

		// Disconnect from previous WebSocket if any
		disconnectWebSocket();

		// Clear previous conversation to ensure fresh load
		conversationStore.clearConversation(platId);
		
		console.log('Loading conversation for platform with tickets:', {
			platformId: platId,
			customerId: custId,
			ticketCount: currentTicketIds?.length || 0,
			ticketIds: currentTicketIds
		});

		try {
			loading = true;

			// Load platform info
			const platformResponse = await fetch(
				`${getBackendUrl()}/customer/api/customers/${custId}/platform-identities/${platId}/`,
				{
					credentials: 'include'
				}
			);

			if (platformResponse.ok) {
				const platformData = await platformResponse.json();

				// Handle both single result and array of results
				const platform = Array.isArray(platformData.results)
					? platformData.results[0]
					: platformData;

				if (platform) {
					customerName = platform.display_name || platform.platform_username || 'Unknown User';
					channelName = platform.channel_name || platform.platform;
				}
			} else {
				console.error('Failed to load platform info');
			}

			// Load messages based on whether we have specific ticket IDs
			if (currentTicketIds && currentTicketIds.length > 0) {
				console.log('Loading conversations for specific ticket IDs:', currentTicketIds);
				await loadConversationForTickets(custId, platId, currentTicketIds);
			} else {
				// Fallback to original single platform conversation loading
				console.log('Loading all conversations for platform');
				await conversationStore.loadConversation(custId, platId);
			}

			// Get the loaded messages to check for unread ones
			const loadedMessages = messages;

			// Mark messages as read
			const unreadMessageIds = loadedMessages
				.filter((msg: Message) => !msg.is_self && msg.status !== 'READ')
				.map((msg: Message) => msg.id);

			if (unreadMessageIds.length > 0) {
				markMessagesAsRead(unreadMessageIds);
			}

			// Connect WebSocket for this platform
			connectWebSocket(custId, platId);
		} catch (err) {
			console.error('Error loading conversation:', err);
			error = 'Failed to load conversation. Please try again.';
		} finally {
			loading = false;
		}
	}

	async function loadConversationForTickets(custId: number, platId: number, ticketIds: number[]) {
		try {
			// Debug logging
			console.log('loadConversationForTickets called with:', {
				custId,
				platId,
				ticketIds,
				access_token: access_token ? 'Present' : 'Missing'
			});

			// Build query parameters for ticket IDs
			const params = new URLSearchParams();
			ticketIds.forEach(id => params.append('ticket_ids[]', id.toString()));
			
			console.log('Loading conversations for ticket IDs:', ticketIds);

			// const response = await fetch(
            //     `${getBackendUrl()}/customer/api/customers/${custId}/platforms/${platId}/conversations/?${params.toString()}`,
            //     {
            //         method: 'GET',
            //         headers: {
            //             'Authorization': `Bearer ${access_token}`,
            //             'Content-Type': 'application/json'
            //         },
            //         credentials: 'include'
            //     }
            // );
			const url = `${getBackendUrl()}/customer/api/customers/${custId}/platforms/${platId}/conversations/?${params.toString()}`;
			console.log('Fetching conversations from:', url);

			const response = await fetch(url, {
				method: 'GET',
				headers: {
					'Authorization': `Bearer ${access_token}`,
					'Content-Type': 'application/json'
				},
				credentials: 'include'
			});

			console.log('Response status:', response.status);

			if (response.ok) {
				const data = await response.json();
				console.log('Conversation data received:', data);
				
				if (data.messages && data.messages.length > 0) {
					// Sort messages by created_on timestamp
					const sortedMessages = data.messages.sort((a: any, b: any) => 
						new Date(a.created_on).getTime() - new Date(b.created_on).getTime()
					);

					console.log('Total messages received from API:', sortedMessages.length);
					console.log('Messages for tickets:', ticketIds);

					// Since we're loading fresh for all tickets, replace existing messages
					// This ensures we get all conversations from all current tickets
					conversationStore.setMessages(platId, sortedMessages, data.has_more || false);
				} else {
					console.log('No messages received from API');
					// Clear messages if no data received
					conversationStore.setMessages(platId, [], false);
				}
			} else {
				const errorText = await response.text();
				console.error('Failed to load conversation for tickets:', {
					status: response.status,
					statusText: response.statusText,
					errorText: errorText
				});
			}
		} catch (error) {
			console.error('Error loading conversation for tickets:', error);
		}
	}

	function connectWebSocket(custId: number, platId: number) {
		// For the global platform WebSocket approach
		if (typeof window !== 'undefined') {
			// Subscribe to this specific platform for updates
			platformWebSocket.subscribeToPlatform(platId);
			connected = true;
		}
	}

	function disconnectWebSocket() {
		// Unsubscribe from the current platform if using global WebSocket
		if (platformId && typeof window !== 'undefined') {
			platformWebSocket.unsubscribeFromPlatform(platformId);
		}
		connected = false;
	}

	async function markMessagesAsRead(messageIds: number[]) {
		try {
			await fetch(
				`${getBackendUrl()}/customer/api/customers/${customerId}/platforms/${platformId}/read/`,
				{
					method: 'POST',
					credentials: 'include',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						message_ids: messageIds
					})
				}
			);

			// Update message status in store
			messageIds.forEach((id) => {
				conversationStore.updateMessageStatus(platformId, id, 'READ');
			});
		} catch (error) {
			console.error('Error marking messages as read:', error);
		}
	}

	async function handleSendMessage(
		event: CustomEvent<{ content: string; type: string; files?: File[] }>
	) {

		// Early return if user cannot send messages
		if (!canSendMessage) {
			console.log('User is not authorized to send messages for this ticket');
			return;
		}

		const { content, type, files } = event.detail;

		try {
			// Show loading state if needed
			const hasFiles = files && files.length > 0;

			// Use conversation service for cleaner code
			const response = await conversationService.sendMessage(
				customerId,
				platformId,
				content,
				type,
				files
			);

			if (response) {
				// Add message to store
				conversationStore.addMessage(platformId, response);

				// Scroll to bottom after sending message
				// This would be handled in MessageList component
			}
		} catch (error) {
			console.error('Error sending message:', error);
			// Show error notification
			// You could add a toast notification here
		}
	}

    // async function handleLoadMore() {
    //     if (loadingMore || !messages.length || !hasMore) return;
    //     const oldestMessage = messages[0];
    //     if (oldestMessage) {
    //         try {
    //             // Use the existing loadMoreMessages method which handles loading state internally
    //             await conversationStore.loadMoreMessages(customerId, platformId, oldestMessage.id);
    //         } catch (error) {
    //             console.error('Error loading more messages:', error);
    //         }
    //     }
    // }
    
	// Handle real-time message updates
	function handleNewMessage(event: CustomEvent) {
		const { platformId: msgPlatformId, message } = event.detail;
		if (msgPlatformId === platformId) {
			// Don't add message here - PlatformIdentityList already handles it
			// Just mark as read if appropriate
			if (document.hasFocus() && !message.is_self && message.status !== 'READ') {
				markMessagesAsRead([message.id]);
			}
		}
	}

	$: canSendMessage = latest_ticket_owner_id === currentLoginUser?.id;
	$: messageInputDisabled = loading || !canSendMessage;

	// console.info('latest_ticket_owner_id', latest_ticket_owner_id);
	// console.info('currentLoginUser', currentLoginUser);
	// console.info('canSendMessage',canSendMessage);
</script>

<div class="flex h-full flex-col">
	<ConversationHeader
		{customerId}
		{customerName}
		{channelName}
		{connected}
		{platformId}
		{ticketId}
		{users}
		{priorities}
		{statuses}
		{topics}
		{access_token}
	/>

	<!-- <MessageList 
		{messages}
		{loading}
		on:loadMore={handleLoadMore}
	/> -->

	<MessageList {messages} {loading} {hasMore} {focusedTicketId} />

	{#if showMessageInput} <!-- Disable for monitoring id page -->
		<MessageInput 
			on:send={handleSendMessage} 
			disabled={messageInputDisabled}
			{canSendMessage}
		/>
	{/if}
</div>
