<script lang="ts">
	/**
	 * MessageList.svelte - Intelligent Scroll Behavior Implementation
	 *
	 * This component implements two distinct scroll scenarios:
	 *
	 * SCENARIO 1: Chat Context Change (platformId changed)
	 * - Show loading overlay while new messages are being fetched
	 * - After messages load, automatically scroll to bottom (instant scroll, not smooth)
	 * - This ensures users always start at the bottom of newly opened chats
	 *
	 * SCENARIO 2: New Message in Current Chat (platformId unchanged)
	 * - Do NOT show any loading overlay
	 * - Implement conditional smooth scrolling behavior:
	 *   - If user is near bottom (within 100px threshold) OR at the very bottom: smooth scroll to show new message
	 *   - If user has scrolled up significantly: preserve their current scroll position (no auto-scroll)
	 */

	import { onMount, afterUpdate, createEventDispatcher, tick } from 'svelte';
	import { t } from '$lib/stores/i18n';

	import MessageItem from './MessageItem.svelte';
	import LoadingSpinner from '../common/LoadingSpinner.svelte';

	import type { Message } from '$lib/types/customer';
	import { formatMessageDate } from '$lib/utils/messageFormatter';

	import { TicketSolid, ArrowDownOutline } from 'flowbite-svelte-icons';

	// Configuration: Number of messages to load per request
	const MESSAGES_PER_LOAD = 1;

	export let platformId: number;
	export let messages: Message[] = [];
	export let loading: boolean = false;
	export let loadingMore: boolean = false;
	export let hasMore: boolean = true;
	export let focusedTicketId: number | null = null;
	export let useScrollAnimation: boolean = false;

	const dispatch = createEventDispatcher();

	let scrollContainer: HTMLElement;
	let shouldScrollToBottom = false; // Start as false to prevent unwanted auto-scrolling on init
	let isNearBottom = true;

	// Enhanced auto-scroll state
	let isInitialLoad = true;
	let previousMessageCount = 0;
	let previousPlatformId = platformId;
	let isLoadMoreOperation = false;
	let isNewMessageScroll = false;
	let isManualScrollToBottom = false;

	// Sticky header state
	let stickyDate: string = '';
	let stickyTicketId: number | null = null;
	let showStickyHeader = true;
	let messageGroupElementsByTicket: HTMLElement[] = [];
	let dateGroupElements: HTMLElement[] = [];

	// Ticket navigation state
	let ticketElements: Map<number, HTMLElement> = new Map();
	let isNavigating = false;
	let scrollInProgress = false;

	// Scroll to bottom button state
	let hasUserScrolledUp = false;
	$: showScrollToBottomButton = hasUserScrolledUp && !isNearBottom;

	// Loading overlay state
	let showLoadingOverlay = false;
	let overlayMessage = '';
	let isOverlayVisible = false;

	// Reactive loading overlay management - INTELLIGENT SCROLL BEHAVIOR
	// SCENARIO 1: Chat Context Change (platformId changed) - Show loading overlay
	// SCENARIO 2: New Message in Current Chat (platformId unchanged) - NO loading overlay
	$: {
		const hasContextChanged = platformId !== previousPlatformId;
		const isInitialLoading = loading && messages.length === 0;

		// ONLY show overlay for platformId changes and initial loading
		// NEVER show overlay for new messages in current chat or smooth scroll scenarios
		const shouldShowOverlay = hasContextChanged || isInitialLoading;

		console.log("Debug: MessageList.svelte: Intelligent scroll overlay management:", {
			hasContextChanged,
			isInitialLoading,
			shouldShowOverlay,
			scenario: hasContextChanged ? 'CONTEXT_CHANGE' : isInitialLoading ? 'INITIAL_LOAD' : 'NEW_MESSAGE_OR_OTHER'
		});

		// Set overlay message based on the operation
		if (hasContextChanged) {
			overlayMessage = t('chat_center_loading_messages') || 'Loading messages...';
		} else if (isInitialLoading) {
			overlayMessage = t('chat_center_loading_messages') || 'Loading messages...';
		} else {
			overlayMessage = '';
		}

		// Update overlay visibility with proper timing
		if (shouldShowOverlay) {
			showLoadingOverlay = true;
			isOverlayVisible = true;
		} else {
			showLoadingOverlay = false;
			// Delay hiding to ensure smooth transitions and prevent flashing
			setTimeout(() => {
				if (!showLoadingOverlay) {
					isOverlayVisible = false;
				}
			}, 150);
		}
	}

	// Additional reactive statement to handle completion states
	$: {
		// Hide overlay when loading states complete
		if (!loading && !loadingMore) {
			showLoadingOverlay = false;
		}
	}

	// Calculate actual bottom spacing by examining DOM elements
	function calculateBottomSpacing(): number {
		if (!scrollContainer) return 32; // fallback to estimated spacing

		try {
			// Get container padding
			const containerStyles = window.getComputedStyle(scrollContainer);
			const containerPaddingBottom = parseInt(containerStyles.paddingBottom) || 0;

			// Find the last message item
			const messageItems = scrollContainer.querySelectorAll('[id^="message-item-container-"]');
			if (messageItems.length === 0) return containerPaddingBottom;

			const lastMessageItem = messageItems[messageItems.length - 1] as HTMLElement;
			const lastMessageStyles = window.getComputedStyle(lastMessageItem);
			const lastMessageMarginBottom = parseInt(lastMessageStyles.marginBottom) || 0;

			// Total bottom spacing = container padding + last message margin
			return containerPaddingBottom + lastMessageMarginBottom;
		} catch (error) {
			console.warn('Error calculating bottom spacing, using fallback:', error);
			return 32; // fallback: 16px container padding + 16px message margin
		}
	}

	// Verify that the last message is fully visible including its bottom margin
	function isLastMessageFullyVisible(): boolean {
		if (!scrollContainer) return false;

		try {
			const messageItems = scrollContainer.querySelectorAll('[id^="message-item-container-"]');
			if (messageItems.length === 0) return true;

			const lastMessageItem = messageItems[messageItems.length - 1] as HTMLElement;
			const containerRect = scrollContainer.getBoundingClientRect();
			const messageRect = lastMessageItem.getBoundingClientRect();

			// Get the bottom margin of the last message
			const messageStyles = window.getComputedStyle(lastMessageItem);
			const marginBottom = parseInt(messageStyles.marginBottom) || 0;

			// Check if the bottom of the message + its margin is visible within the container
			const messageBottomWithMargin = messageRect.bottom + marginBottom;
			const containerBottom = containerRect.bottom;

			// Account for container padding
			const containerStyles = window.getComputedStyle(scrollContainer);
			const paddingBottom = parseInt(containerStyles.paddingBottom) || 0;

			return messageBottomWithMargin <= (containerBottom - paddingBottom + 5); // 5px tolerance
		} catch (error) {
			console.warn('Error checking last message visibility:', error);
			return false;
		}
	}

	// Debug function to log scroll state information
	function logScrollDebugInfo(context: string = '') {
		if (!scrollContainer) return;

		const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
		const bottomSpacing = calculateBottomSpacing();
		const lastMessageVisible = isLastMessageFullyVisible();
		const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

		console.log(`[MessageList Scroll Debug] ${context}`, {
			scrollTop,
			scrollHeight,
			clientHeight,
			bottomSpacing,
			distanceFromBottom,
			lastMessageVisible,
			isAtBottom: isAtBottom(),
			targetScrollTop: scrollHeight - clientHeight + bottomSpacing
		});
	}

	onMount(() => {
		// Listen for ticket navigation events
		if (typeof window !== 'undefined') {
			window.addEventListener('navigate-to-ticket', handleNavigateToTicket as EventListener);
		}

		// Cleanup on destroy
		return () => {
			if (typeof window !== 'undefined') {
				window.removeEventListener('navigate-to-ticket', handleNavigateToTicket as EventListener);
			}
		};
	});

	// Context-aware scroll manager - system scrolling has priority
	async function performScroll(action: 'ticket-end' | 'bottom', ticketId?: number, forceInstant: boolean = false, isNewMessage: boolean = false, isManualScroll: boolean = false) {
		// If a scroll is already in progress, wait for it to complete
		if (scrollInProgress) {
			return;
		}

		scrollInProgress = true;

		try {
			if (action === 'ticket-end' && ticketId) {
				await scrollToTicketEnd(ticketId);
			} else if (action === 'bottom') {
				await executeBottomScrollWithOverlay(forceInstant, isNewMessage, isManualScroll);
				// Reset hasUserScrolledUp when scrolling to bottom (both auto and manual)
				hasUserScrolledUp = false;
			}
		} finally {
			scrollInProgress = false;
			// Reset scroll state flags after scroll operation completes
			isNewMessageScroll = false;
			isManualScrollToBottom = false;
		}
	}

	async function scrollToTicketEnd(ticketId: number): Promise<void> {
		const ticketElement = ticketElements.get(ticketId);
		
		if (ticketElement && scrollContainer) {
			isNavigating = true;
			shouldScrollToBottom = false;
			
			// Set focused ticket for visual highlighting
			focusedTicketId = ticketId;
			
			// Scroll to the end of the specific ticket with proper spacing
			const targetScrollTop = Math.max(0, 
				ticketElement.offsetTop + ticketElement.offsetHeight - scrollContainer.clientHeight + 100
			);
			
			scrollContainer.scrollTo({
				top: targetScrollTop,
				behavior: 'smooth'
			});
			
			// Wait for scroll animation to complete
			await new Promise(resolve => {
				setTimeout(() => {
					isNavigating = false;
					// Use enhanced bottom detection for consistency
					shouldScrollToBottom = isAtBottom(10);
					resolve(void 0);
				}, 1000);
			});
		}
	}

	// Wait for all images in the scroll container to load
	async function waitForImages(): Promise<void> {
		if (!scrollContainer) return;

		const images = scrollContainer.querySelectorAll('img');
		if (images.length === 0) return;

		const imagePromises = Array.from(images).map(img => {
			if (img.complete) return Promise.resolve();
			return new Promise<void>(resolve => {
				img.onload = () => resolve();
				img.onerror = () => resolve(); // Still resolve on error to not block
				// Timeout fallback in case image never loads
				setTimeout(() => resolve(), 2000);
			});
		});

		await Promise.all(imagePromises);
	}

	// Wait for all content to be fully rendered and sized including margins/padding
	async function waitForAllContent(): Promise<void> {
		if (!scrollContainer) return;

		// Wait for images to load first
		await waitForImages();

		// Wait for DOM updates to complete using multiple animation frames
		// This ensures all dynamic content has been rendered and sized
		await new Promise<void>(resolve => {
			requestAnimationFrame(() => {
				requestAnimationFrame(() => {
					requestAnimationFrame(() => {
						resolve();
					});
				});
			});
		});

		// Wait for layout calculations to complete, especially for margins and padding
		// This is crucial for MessageItem mb-4 margins to be properly calculated
		await new Promise<void>(resolve => setTimeout(resolve, 100));

		// Additional check: ensure scroll height has stabilized
		let previousScrollHeight = scrollContainer.scrollHeight;
		await new Promise<void>(resolve => {
			const checkStability = () => {
				const currentScrollHeight = scrollContainer.scrollHeight;
				if (currentScrollHeight === previousScrollHeight) {
					resolve();
				} else {
					previousScrollHeight = currentScrollHeight;
					setTimeout(checkStability, 25);
				}
			};
			setTimeout(checkStability, 25);
		});
	}

	// Enhanced bottom detection with proper spacing consideration
	function isAtBottom(tolerance: number = 10): boolean {
		if (!scrollContainer) return false;

		const { scrollTop, scrollHeight, clientHeight } = scrollContainer;

		// Get actual bottom spacing from DOM
		const bottomSpacing = calculateBottomSpacing();
		const adjustedTolerance = tolerance + bottomSpacing;

		// Method 1: Standard calculation with spacing adjustment
		const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
		const method1 = distanceFromBottom <= adjustedTolerance;

		// Method 2: Direct position check with spacing
		const method2 = scrollTop >= (scrollHeight - clientHeight - adjustedTolerance);

		// Method 3: Check if we're within the bottom spacing area
		const method3 = distanceFromBottom <= bottomSpacing + 10; // Allow 10px additional tolerance

		// Method 4: Percentage-based check (within 99% of total scroll to account for spacing)
		const scrollPercentage = scrollTop / (scrollHeight - clientHeight);
		const method4 = scrollPercentage >= 0.99;

		// Method 5: Check if last message is fully visible (most reliable)
		const method5 = isLastMessageFullyVisible();

		// Return true if any method confirms we're at bottom
		return method1 || method2 || method3 || method4 || method5;
	}



	afterUpdate(() => {
		const hasNewMessages = messages.length > previousMessageCount;
		const hasContextChanged = platformId !== previousPlatformId;

		// SCENARIO 1: Chat Context Change (platformId changed)
		// Show loading overlay while new messages are being fetched
		// After messages load, automatically scroll to bottom (instant scroll)
		// This ensures users always start at the bottom of newly opened chats
		if (hasContextChanged) {
			console.log('MessageList: SCENARIO 1 - Chat Context Change detected');
			isNewMessageScroll = false; // Reset for context change
			hasUserScrolledUp = false; // Reset scroll state for new context
			if (messages.length > 0) {
				performScroll('bottom', undefined, true); // forceInstant = true for context change
			}
			isInitialLoad = false;
			isLoadMoreOperation = false;
			previousPlatformId = platformId;
		}
		// Initial load - always use instant scroll
		else if (isInitialLoad && messages.length > 0) {
			console.log('MessageList: Initial load detected');
			isNewMessageScroll = false; // Reset for initial load
			hasUserScrolledUp = false; // Reset scroll state for initial load
			performScroll('bottom', undefined, true); // forceInstant = true
			isInitialLoad = false;
		}
		// SCENARIO 2: New Message in Current Chat (platformId unchanged)
		// Do NOT show any loading overlay
		// Implement conditional smooth scrolling behavior:
		// - If user is near bottom (within 100px threshold) OR at the very bottom: smooth scroll to show new message
		// - If user has scrolled up significantly: preserve their current scroll position (no auto-scroll)
		else if (hasNewMessages && !isNavigating && focusedTicketId === null && !isLoadMoreOperation) {
			console.log('MessageList: SCENARIO 2 - New Message in Current Chat detected', {
				isNearBottom,
				isAtBottomCheck: isAtBottom(),
				shouldAutoScroll: isNearBottom || isAtBottom()
			});

			// Check if user is near bottom (within 100px) OR at the very bottom
			if (isNearBottom || isAtBottom()) {
				// User is near bottom - smooth scroll to show new message
				isNewMessageScroll = true;
				hasUserScrolledUp = false; // Reset when auto-scrolling due to new messages
				performScroll('bottom', undefined, false, true); // forceInstant = false, isNewMessage = true (smooth scroll)
			} else {
				// User has scrolled up significantly - preserve their current scroll position (no auto-scroll)
				console.log('MessageList: User has scrolled up significantly, preserving scroll position');
				isNewMessageScroll = false;
			}
		}
		else {
			// No relevant scroll action, reset the flag
			isNewMessageScroll = false;
		}

		// Reset load more operation flag after processing
		if (isLoadMoreOperation && hasNewMessages) {
			isLoadMoreOperation = false;
		}

		// Update message count for next comparison
		previousMessageCount = messages.length;

		// Update sticky header after DOM updates
		updateStickyHeader();
	});



	// Handle ticket selection from left panel - always scroll to ticket end
	function handleNavigateToTicket(event: Event) {
		const customEvent = event as CustomEvent;
		const { ticketId } = customEvent.detail;
		shouldScrollToBottom = false;

		// Always scroll to the end of the selected ticket
		performScroll('ticket-end', ticketId);
	}

	// Handle scroll to bottom button click
	function handleScrollToBottomClick() {
		isManualScrollToBottom = true;
		hasUserScrolledUp = false; // Reset when user manually scrolls to bottom
		performScroll('bottom', undefined, false, false, true); // forceInstant=false, isNewMessage=false, isManualScroll=true
	}



	function handleScroll(event: Event) {
		if (!scrollContainer) return;

		// Prevent manual scrolling when system is scrolling
		if (scrollInProgress) {
			event.preventDefault();
			return;
		}

		const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
		const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

		// Check if user is near bottom (within 100px) - keep existing threshold for compatibility
		const wasNearBottom = isNearBottom;
		isNearBottom = distanceFromBottom < 100;

		// Track manual scrolling: user has scrolled up if they moved away from bottom
		// Only set hasUserScrolledUp when user manually scrolls up (not when auto-scrolling)
		if (!wasNearBottom && !isNearBottom) {
			// User is scrolling while already away from bottom - maintain hasUserScrolledUp state
		} else if (wasNearBottom && !isNearBottom) {
			// User just scrolled up from being near bottom - this is a manual scroll up
			hasUserScrolledUp = true;
		} else if (!wasNearBottom && isNearBottom) {
			// User scrolled back to near bottom - reset the flag
			hasUserScrolledUp = false;
		}

		// Check if scrolled to top for loading more
		if (scrollTop === 0 && messages.length > 0 && hasMore && !loadingMore) {
			isLoadMoreOperation = true;
			dispatch('loadMore', {
				limit: MESSAGES_PER_LOAD
			});
		}

		// Update sticky header based on scroll position
		updateStickyHeader();
	}

	function updateStickyHeader() {
		if (!scrollContainer || dateGroupElements.length === 0) return;

		const scrollTop = scrollContainer.scrollTop;
		const containerTop = scrollContainer.getBoundingClientRect().top;

		// Find the topmost visible date group
		let currentDateGroup = null;
		let currentTicketId = null;
		let validElementsFound = 0;

		// Get all date groups once to avoid repeated calls
		const allDateGroups = getAllDateGroups();

		// Defensive check: ensure we have date groups to work with
		if (allDateGroups.length === 0) {
			showStickyHeader = false;
			return;
		}

		for (let i = 0; i < dateGroupElements.length; i++) {
			const element = dateGroupElements[i];

			// Skip undefined elements (can happen during DOM updates)
			if (!element) {
				continue;
			}

			validElementsFound++;

			// Defensive check: ensure we don't exceed the allDateGroups array bounds
			if (i >= allDateGroups.length) {
				console.warn('MessageList: dateGroupElements index exceeds allDateGroups length', {
					elementIndex: i,
					allDateGroupsLength: allDateGroups.length,
					dateGroupElementsLength: dateGroupElements.length
				});
				break;
			}

			const rect = element.getBoundingClientRect();
			const elementTop = rect.top - containerTop;

			// If this date group is visible or partially visible at the top
			if (elementTop <= 20) {
				// 20px threshold for sticky header activation
				currentDateGroup = allDateGroups[i];

				// Defensive check: ensure currentDateGroup is valid
				if (currentDateGroup && currentDateGroup.messages && currentDateGroup.messages.length > 0) {
					// Find the ticket ID for this date group
					for (const ticketGroup of messageGroupedByTicketAndDate) {
						if (ticketGroup.dateGroups.includes(currentDateGroup)) {
							currentTicketId = ticketGroup.ticketId;
							break;
						}
					}
				}
			} else {
				break;
			}
		}

		// Log warning if no valid elements were found but array has length
		if (validElementsFound === 0 && dateGroupElements.length > 0) {
			console.warn('MessageList: No valid DOM elements found in dateGroupElements array', {
				arrayLength: dateGroupElements.length,
				totalDateGroups: allDateGroups.length
			});
		}

		if (currentDateGroup && currentDateGroup.messages && currentDateGroup.messages.length > 0 && scrollTop > 50) {
			// Only show sticky header after scrolling 50px
			showStickyHeader = true;
			stickyDate = formatMessageDate(currentDateGroup.messages[0].created_on);
			stickyTicketId = currentTicketId;
		} else {
			showStickyHeader = false;
		}
	}

	// Helper function to get all date groups in a flat array for tracking
	function getAllDateGroups() {
		const allDateGroups: { date: string; messages: Message[] }[] = [];
		messageGroupedByTicketAndDate.forEach((ticketGroup) => {
			ticketGroup.dateGroups.forEach((dateGroup) => {
				allDateGroups.push(dateGroup);
			});
		});
		return allDateGroups;
	}

	// Helper function to calculate global date group index
	function getGlobalDateGroupIndex(ticketGroupIndex: number, dateGroupIndex: number): number {
		let globalIndex = 0;
		for (let i = 0; i < ticketGroupIndex; i++) {
			globalIndex += messageGroupedByTicketAndDate[i].dateGroups.length;
		}
		return globalIndex + dateGroupIndex;
	}

	// Group messages by ticket, then by date within each ticket
	function groupMessagesByTicketAndDate(messages: Message[]) {
		const groups: {
			ticketId: number;
			dateGroups: { date: string; messages: Message[] }[];
		}[] = [];
		let currentTicketId = -1;

		messages.forEach((msg) => {
			const msgDate = new Date(msg.created_on).toLocaleDateString();

			if (msg.ticket_id !== currentTicketId) {
				// New ticket group
				currentTicketId = msg.ticket_id;
				groups.push({
					ticketId: currentTicketId,
					dateGroups: [
						{
							date: msgDate,
							messages: [msg]
						}
					]
				});
				// For Debugging
				// console.log('MessageList.svelte: groupMessagesByTicketAndDate(): New ticket group created:', groups[groups.length - 1]);
			} else {
				// Same ticket, check if we need a new date group
				const currentTicketGroup = groups[groups.length - 1];
				const lastDateGroup =
					currentTicketGroup.dateGroups[currentTicketGroup.dateGroups.length - 1];

				if (lastDateGroup.date !== msgDate) {
					// New date within the same ticket
					currentTicketGroup.dateGroups.push({
						date: msgDate,
						messages: [msg]
					});
				} else {
					// Same date, add to existing date group
					lastDateGroup.messages.push(msg);
				}
			}
		});
		return groups;
	}

	// Check if should show avatar (first message or different sender)
	function shouldShowAvatar(message: Message, index: number, messages: Message[]) {
		if (index === 0) return true;
		const prevMessage = messages[index - 1];
		return prevMessage.is_self !== message.is_self || prevMessage.user_name !== message.user_name;
	}

	$: messageGroupedByTicketAndDate = groupMessagesByTicketAndDate(messages);

	// Calculate total number of date groups across all tickets
	$: totalDateGroups = messageGroupedByTicketAndDate.reduce((total, ticketGroup) => {
		return total + ticketGroup.dateGroups.length;
	}, 0);

	// Ensure messageGroupElementsByTicket array is properly sized for the new nested structure
	$: if (messageGroupedByTicketAndDate.length !== messageGroupElementsByTicket.length) {
		const newArray = new Array(messageGroupedByTicketAndDate.length);
		// Copy existing references to the new array
		for (let i = 0; i < Math.min(messageGroupElementsByTicket.length, newArray.length); i++) {
			newArray[i] = messageGroupElementsByTicket[i];
		}
		messageGroupElementsByTicket = newArray;
	}

	// Ensure dateGroupElements array is properly sized while preserving existing references
	$: if (totalDateGroups !== dateGroupElements.length) {
		const newArray = new Array(totalDateGroups);
		// Copy existing references to the new array where possible
		// Note: When messages are prepended, indices shift, but we preserve what we can
		for (let i = 0; i < Math.min(dateGroupElements.length, newArray.length); i++) {
			newArray[i] = dateGroupElements[i];
		}
		dateGroupElements = newArray;
	}

	// Update ticketElements map when messageGroupElementsByTicket changes
	$: if (messageGroupElementsByTicket && messageGroupedByTicketAndDate.length > 0) {
		ticketElements.clear();
		messageGroupedByTicketAndDate.forEach((ticketGroup, index) => {
			if (messageGroupElementsByTicket[index]) {
				ticketElements.set(ticketGroup.ticketId, messageGroupElementsByTicket[index]);
			}
		});
	}

	// Refresh sticky header after load more operation completes
	$: if (isLoadMoreOperation && messages.length > previousMessageCount) {
		// Load more operation has completed (new messages added)
		refreshStickyHeaderAfterLoadMore();
	}

	async function refreshStickyHeaderAfterLoadMore() {
		// Wait for DOM to be fully updated
		await tick();

		// Give additional time for DOM element bindings to complete
		requestAnimationFrame(() => {
			requestAnimationFrame(() => {
				// Reset the load more flag
				isLoadMoreOperation = false;

				// Update sticky header with the new DOM structure
				updateStickyHeader();
			});
		});
	}

	// Enhanced scroll completion handler with proper bottom spacing calculation
	async function executeBottomScrollWithOverlay(forceInstant: boolean = false, isNewMessage: boolean = false, isManualScroll: boolean = false): Promise<void> {
		if (!scrollContainer) return;

		// Wait for all content to be fully rendered
		await waitForAllContent();

		return new Promise(resolve => {
			const attemptScroll = (attempt = 0) => {
				requestAnimationFrame(() => {
					try {
						const { scrollHeight, clientHeight } = scrollContainer;

						// Calculate proper target scroll position accounting for actual bottom spacing
						const bottomSpacing = calculateBottomSpacing();
						const targetScrollTop = Math.max(0, scrollHeight - clientHeight + bottomSpacing);

						// Debug logging for troubleshooting
						if (attempt === 0) {
							logScrollDebugInfo(`Scroll attempt ${attempt + 1} - Before scroll`);
						}

						// Determine scroll behavior based on context and animation setting
						// Force smooth scrolling for new messages and manual scroll-to-bottom
						const scrollBehavior = (isNewMessage || isManualScroll) ? 'smooth' : (forceInstant ? 'auto' : (useScrollAnimation ? 'smooth' : 'auto'));

						scrollContainer.scrollTo({
							top: targetScrollTop,
							behavior: scrollBehavior as ScrollBehavior
						});

						// Verify scroll completion with multiple checks
						const checkScrollComplete = () => {
							const currentScrollTop = scrollContainer.scrollTop;
							const tolerance = 8; // Slightly larger tolerance for better reliability
							const isAtTargetPosition = Math.abs(currentScrollTop - targetScrollTop) <= tolerance;

							// Also check if we're at the maximum possible scroll position
							const maxScrollTop = scrollHeight - clientHeight;
							const isAtMaxScroll = Math.abs(currentScrollTop - maxScrollTop) <= tolerance;

							// Most importantly, check if the last message is fully visible
							const lastMessageVisible = isLastMessageFullyVisible();

							// Debug logging for completion check
							logScrollDebugInfo(`Scroll completion check - attempt ${attempt + 1}`);

							if ((isAtTargetPosition || isAtMaxScroll || lastMessageVisible) || attempt >= 3) {
								logScrollDebugInfo(`Scroll completed successfully`);
								resolve();
							} else if (attempt < 3) {
								setTimeout(() => attemptScroll(attempt + 1), 50);
							} else {
								// Final attempt: ensure we're at the absolute bottom with extra scroll
								const finalScrollTop = Math.max(targetScrollTop, scrollHeight - clientHeight + 50);
								scrollContainer.scrollTo({
									top: finalScrollTop,
									behavior: 'auto'
								});
								logScrollDebugInfo(`Final scroll attempt to position ${finalScrollTop}`);
								setTimeout(() => resolve(), 100);
							}
						};

						if (scrollBehavior === 'smooth') {
							// For smooth scrolling, wait for animation to complete
							setTimeout(checkScrollComplete, 300);
						} else {
							// For instant scrolling, check immediately
							setTimeout(checkScrollComplete, 10);
						}
					} catch (error) {
						console.error('Error during scroll execution:', error);
						resolve();
					}
				});
			};

			attemptScroll();
		});
	}
</script>

<div class="message-list-wrapper relative flex-1 flex flex-col">
	<!-- Loading Overlay -->
	{#if isOverlayVisible}
		<div
			id="message-list-loading-overlay"
			class="absolute inset-0 z-50 flex flex-col items-center justify-center bg-white backdrop-blur-sm transition-opacity duration-200"
			class:opacity-100={showLoadingOverlay}
			class:opacity-0={!showLoadingOverlay}
			role="status"
			aria-live="polite"
			aria-label={overlayMessage || 'Loading'}
		>
			<div class="flex flex-col items-center space-y-4">
				<LoadingSpinner size="lg" />
			</div>
		</div>
	{/if}

	<div
		id="message-list-container"
		bind:this={scrollContainer}
		on:scroll={handleScroll}
		class="custom-scrollbar flex-1 overflow-y-auto bg-gray-50 px-6 py-4 {scrollInProgress ? 'disable-scroll' : ''} {useScrollAnimation ? 'smooth-scroll' : 'instant-scroll'}"
	>
	<!-- Sticky Header -->
	{#if showStickyHeader}
		<div id="message-list-sticky-header" class="sticky top-0 z-10 mb-4 pb-2 transition-all duration-200">
			<div class="my-4 flex items-center justify-center">
				<span
					id="message-list-sticky-date"
					class="flex items-center justify-center rounded-full bg-gray-900 bg-opacity-40 px-3 py-1 text-center text-xs text-white"
				>
					{#if stickyTicketId}
						<TicketSolid class="mr-2 h-4 w-4" />
						#{stickyTicketId} • 
					{/if}
					{stickyDate}
				</span>
			</div>
		</div>
	{/if}
	{#if loading && messages.length === 0}
		<div id="message-list-initial-loading" class="flex h-full items-center justify-center">
			<LoadingSpinner />
		</div>
	{:else}
		<!-- Load more indicator at top -->
		{#if loadingMore}
			<div id="message-list-load-more-indicator" class="flex items-center justify-center py-3 text-gray-500">
				<LoadingSpinner size="sm" />
				<!-- <span class="ml-2 text-sm">{t('chat_center_loading_more_messages')}</span> -->
			</div>
		{/if}

		<!-- Messages grouped by ticket and date -->
		{#each messageGroupedByTicketAndDate as ticketGroup, ticketGroupIndex}
			{#if ticketGroup.ticketId}
				<div 
					bind:this={messageGroupElementsByTicket[ticketGroupIndex]} 
					class="message-group {focusedTicketId === ticketGroup.ticketId ? 'focused-ticket' : ''}"
					id="message-list-ticket-group-{ticketGroup.ticketId}" 
				>
					<!-- Ticket separator -->
					<div id="message-list-ticket-separator-{ticketGroup.ticketId}" class="my-10 my-4 flex items-center justify-center">
						<span
							class="flex items-center justify-center rounded-full transition-all duration-500
								{focusedTicketId === ticketGroup.ticketId 
									? 'bg-blue-600 px-4 py-2 text-sm shadow-lg border-2 border-blue-300' 
									: 'bg-gray-900 bg-opacity-40 px-3 py-1 text-xs'} 
								text-white"
							id="message-list-ticket-badge-{ticketGroup.ticketId}"	   
						>
							<TicketSolid class="mr-2 h-5 w-5" />
							{ticketGroup.ticketId}
						</span>
					</div>

					<!-- Date groups within ticket -->
					{#each ticketGroup.dateGroups as dateGroup, dateGroupIndex}
						{@const globalDateGroupIndex = getGlobalDateGroupIndex(ticketGroupIndex, dateGroupIndex)}
						<!-- Date separator -->
						<div
							id="message-list-date-separator-{dateGroup.date.replace(/\//g, '-')}"
							bind:this={dateGroupElements[globalDateGroupIndex]}
							class="my-4 flex items-center justify-center"
						>
							<span
								id="message-list-date-badge-{dateGroup.date.replace(/\//g, '-')}"
								class="flex items-center justify-center rounded-full bg-gray-900 bg-opacity-40 px-3 py-1 text-center text-xs text-white"
							>
								{formatMessageDate(dateGroup.messages[0].created_on)}
							</span>
						</div>

						<!-- Messages in date group -->
						<div id="message-list-messages-{dateGroup.date.replace(/\//g, '-')}">
							{#each dateGroup.messages as message, messageIndex}
								<MessageItem
									{message}
									showAvatar={shouldShowAvatar(message, messageIndex, dateGroup.messages)}
								/>
							{/each}
						</div>
					{/each}
				</div>
			{/if}
		{/each}

		{#if messages.length === 0}
			<div id="message-list-empty-state" class="mt-8 text-center text-gray-500">
				{t('no_messages')}
			</div>
		{/if}
	{/if}
	</div>

	<!-- Floating Scroll to Bottom Button -->
	{#if showScrollToBottomButton}
		<button
			id="message-list-scroll-to-bottom-button"
			class="scroll-to-bottom-button z-20 flex h-8 w-8 items-center justify-center rounded bg-gray-900 bg-opacity-80 text-white shadow-lg transition-all duration-300 hover:bg-opacity-100 hover:shadow-xl focus:outline-none"
			on:click={handleScrollToBottomClick}
			aria-label={t('chat_center_scroll_to_bottom')}
			title={t('chat_center_scroll_to_bottom')}
		>
			<ArrowDownOutline class="h-6 w-6" />
		</button>
	{/if}
</div>

<style>
	/* Dynamic scroll behavior classes */
	.smooth-scroll {
		scroll-behavior: smooth;
	}

	.instant-scroll {
		scroll-behavior: auto;
	}

	/* Disable scrolling when system is scrolling */
	.disable-scroll {
		pointer-events: none;
		user-select: none;
	}

	/* Focused ticket styling */
	.focused-ticket {
		border-left: 4px solid #3b82f6;
		padding-left: 1.5rem;
		margin-left: -1.5rem;
		border-radius: 0.5rem;
	}

	/* Scroll to bottom button positioning and animations */
	.scroll-to-bottom-button {
		animation: fadeInUp 0.3s ease-out;
		/* Position relative to the wrapper container's visible area */
		position: absolute;
		bottom: 0.8rem;
		right: 1.5rem;
		/* Ensure it stays in place during scroll */
		transform: translateZ(0);
		will-change: transform;
	}

	/* Ensure the wrapper can contain absolutely positioned elements and handles flex layout */
	.message-list-wrapper {
		position: relative;
		min-height: 0; /* Allow flex child to shrink below content size */
	}

	@keyframes fadeInUp {
		from {
			opacity: 0;
			transform: translateY(10px) translateZ(0);
		}
		to {
			opacity: 1;
			transform: translateY(0) translateZ(0);
		}
	}

	/* Loading overlay styles */
	#message-list-loading-overlay {
		backdrop-filter: blur(2px);
		-webkit-backdrop-filter: blur(2px);
		transition: opacity 200ms ease-in-out, backdrop-filter 200ms ease-in-out;
	}

	#message-list-loading-overlay.opacity-0 {
		pointer-events: none;
	}
</style>
