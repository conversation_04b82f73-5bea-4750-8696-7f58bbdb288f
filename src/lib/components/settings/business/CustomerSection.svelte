<script lang="ts">
    import {
        Accordion,
        AccordionItem,
    } from 'flowbite-svelte';
    import CustomerTag from './CustomerTag.svelte';

    // Partners variables & functions
    export let customerTagNames = [];
    export let colorOptions: ColorOption[] = [];
</script>

<div class="space-y-4 p-6 bg-white rounded-lg shadow-md"> 
    <Accordion flush>
        <CustomerTag {customerTagNames} />
    </Accordion>
</div>