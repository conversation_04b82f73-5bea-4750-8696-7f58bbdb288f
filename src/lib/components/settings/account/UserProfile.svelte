<script lang="ts">
	import { t } from '$lib/stores/i18n';
	import { languagePreference } from '$lib/stores/languagePreference';
	import { toastStore } from '$lib/stores/toastStore';
	import { parseErrorMessages } from '$lib/utils/errorParser';
	import { invalidateAll } from '$app/navigation';

	import { enhance } from '$app/forms';
	import { Button, Label, Input, Alert, Modal, Select } from 'flowbite-svelte';
	import { EyeSolid, EyeSlashSolid, CheckOutline } from 'flowbite-svelte-icons';

	export let user: any;

	// messages + loading
	let isSubmitting = false;
	let isPasswordSubmitting = false;

	// State variables for handling error messages (following UserSignUp pattern)
	let showErrorMessage = false;
	let errorMessage = '';
	let fieldErrors: Record<string, string[]> = {};

	// form data (expanded to include all fields from UserSignUp.svelte)
	let formData = {
		id: user.id,
		username: user.username,
		first_name: user.first_name,
		last_name: user.last_name,
		is_active: user.is_active,

		// Contact Information
		personal_phone: user.personal_phone || '',
		work_phone: user.work_phone || '',
		personal_email: user.personal_email || '',
		work_email: user.work_email || '',

		// User Preferences
		preferred_language: user.preferred_language || 'th',

		// Emergency Contact Information
		emergency_contact_name: user.emergency_contact_name || '',
		emergency_contact_phone: user.emergency_contact_phone || '',
		emergency_contact_email: user.emergency_contact_email || ''
	};

	// Track initial form data for change detection
	let initialFormData = { ...formData };

	// Password modal state
	let passwordModalOpen = false;
	let passwordFormData = {
		old_password: '',
		new_password: '',
		confirm_password: ''
	};
	let passwordFieldsEverTyped = false;

	// Password visibility toggles
	let showCurrentPassword = false;
	let showNewPassword = false;
	let showConfirmPassword = false;

	// Validation errors for contact fields (following UserSignUp pattern)
	let validationErrors = {
		personal_phone: '',
		personal_email: '',
		work_phone: '',
		work_email: '',
		email: '',
		emergency_contact_phone: '',
		emergency_contact_email: ''
	};

	// Language options (from UserSignUp.svelte)
	$: languageOptions = [
		{ value: 'en', name: t('language_name_en') },
		{ value: 'th', name: t('language_name_th') }
	];

	// Reactive statement to detect changes from initial state
	$: hasChanges = JSON.stringify(formData) !== JSON.stringify(initialFormData);

	// Validation functions (from UserSignUp.svelte)
	function validatePhoneNumber(phone: string) {
		if (!phone.trim()) return { isValid: true, error: '' }; // Optional field
		if (phone.length > 20)
			return { isValid: false, error: 'Phone number must be 20 characters or less' };
		return { isValid: true, error: '' };
	}

	// Email validation function (from UserSignUp.svelte)
	function validateEmail(email: string) {
		if (!email.trim()) return { isValid: true, error: '' }; // Optional field for some
		const emailRegex = /^[a-z0-9](?:[a-z0-9_\-\.]*[a-z0-9])?@[^\s@]+\.[^\s@]+$/i;
		if (!emailRegex.test(email)) {
			return { isValid: false, error: t('signup_error_invalid_email') };
		}
		return { isValid: true, error: '' };
	}

	// Function to dismiss all error alerts when user starts typing
	function dismissAlerts() {
		showErrorMessage = false;
		errorMessage = '';
		fieldErrors = {};
		// Clear validation errors
		validationErrors = {
			personal_phone: '',
			personal_email: '',
			work_phone: '',
			work_email: '',
			email: '',
			emergency_contact_phone: '',
			emergency_contact_email: ''
		};
	}

	// Initialize form data when component mounts (only run once)
	let isInitialized = false;
	$: if (user && !isInitialized) {
		const newFormData = {
			id: user.id,
			name: user.name,
			username: user.username,
			employee_id: user.employee_id,
			first_name: user.first_name,
			last_name: user.last_name,
			// department: user.department || '',
			// role: user.role,
			is_active: user.is_active,

			// Contact Information
			personal_phone: user.personal_phone || '',
			work_phone: user.work_phone || '',
			personal_email: user.personal_email || '',
			work_email: user.work_email || '',

			// User Preferences
			preferred_language: user.preferred_language || 'th',

			// Emergency Contact Information
			emergency_contact_name: user.emergency_contact_name || '',
			emergency_contact_phone: user.emergency_contact_phone || '',
			emergency_contact_email: user.emergency_contact_email || ''
		};

		// Only update form data on initial load
		formData = newFormData;
		initialFormData = { ...newFormData };
		isInitialized = true;
	}

	// Function to update form data with locally saved values (called after successful updates)
	function updateFormDataFromLocalSave(savedFormData: typeof formData) {
		// Use the locally captured form data that was just successfully saved
		formData = { ...savedFormData };
		initialFormData = { ...savedFormData };
	}

	// Name input handler with filtering (from UserSignUp.svelte)
	function handleNameInput(event: Event, fieldName: 'name' | 'first_name' | 'last_name') {
		const target = event.target as HTMLInputElement;

		let filteredValue = '';
		// Allow last_name to contain whitespace
		if (fieldName === 'name' || fieldName === 'last_name') {
			filteredValue = target.value.replace(/[^a-zA-Z\s]/g, '');
		} else {
			filteredValue = target.value.replace(/[^a-zA-Z]/g, '');
		}

		// Update form data with filtered value
		formData[fieldName] = filteredValue;

		// Clear field-specific errors
		if (fieldErrors[fieldName]) {
			fieldErrors[fieldName] = [];
			delete fieldErrors[fieldName];
		}

		// Dismiss alerts
		dismissAlerts();
	}

	// Phone input handler with filtering and validation (enhanced from existing)
	function handlePhoneInput(event: Event, fieldName: 'personal_phone' | 'work_phone') {
		const target = event.target as HTMLInputElement;
		const filteredValue = target.value.replace(/[^0-9+]/g, '');

		// Update form data with filtered value
		formData[fieldName] = filteredValue;

		// Validate and update error state
		const validation = validatePhoneNumber(filteredValue);
		validationErrors[fieldName] = validation.error;

		// Clear field-specific errors
		if (fieldErrors[fieldName]) {
			fieldErrors[fieldName] = [];
			delete fieldErrors[fieldName];
		}

		// Dismiss alerts
		dismissAlerts();
	}

	// Email input handler with validation (enhanced to support all email fields)
	function handleEmailInput(
		event: Event,
		fieldName: 'email' | 'personal_email' | 'work_email' | 'emergency_contact_email'
	) {
		const target = event.target as HTMLInputElement;

		// Validate and update error state
		const validation = validateEmail(target.value);
		validationErrors[fieldName] = validation.error;

		// Clear field-specific errors
		if (fieldErrors[fieldName]) {
			fieldErrors[fieldName] = [];
			delete fieldErrors[fieldName];
		}

		// Dismiss alerts
		dismissAlerts();
	}

	// Emergency contact name input handler (similar to name fields)
	function handleEmergencyNameInput(event: Event) {
		const target = event.target as HTMLInputElement;
		// Allow spaces in emergency contact names
		const filteredValue = target.value.replace(/[^a-zA-Z\s]/g, '');

		// Update form data with filtered value
		formData.emergency_contact_name = filteredValue;

		// Clear field-specific errors
		if (fieldErrors.emergency_contact_name) {
			fieldErrors.emergency_contact_name = [];
			delete fieldErrors.emergency_contact_name;
		}

		// Dismiss alerts
		dismissAlerts();
	}

	// Emergency contact phone input handler
	function handleEmergencyPhoneInput(event: Event) {
		const target = event.target as HTMLInputElement;
		const filteredValue = target.value.replace(/[^0-9+]/g, '');

		// Update form data with filtered value
		formData.emergency_contact_phone = filteredValue;

		// Validate and update error state
		const validation = validatePhoneNumber(filteredValue);
		validationErrors.emergency_contact_phone = validation.error;

		// Clear field-specific errors
		if (fieldErrors.emergency_contact_phone) {
			fieldErrors.emergency_contact_phone = [];
			delete fieldErrors.emergency_contact_phone;
		}

		// Dismiss alerts
		dismissAlerts();
	}

	// Enhanced options following UserSignUp pattern
	$: enhanceOptions = {
		modalOpen: passwordModalOpen,
		setModalOpen: (value: boolean) => (passwordModalOpen = value),
		setPending: (value: boolean, isPassword: boolean = false) =>
			isPassword ? (isPasswordSubmitting = value) : (isPasswordSubmitting = value),
		setShowErrorMessage: (value: boolean) => (showErrorMessage = value),
		setErrorMessage: (value: string) => {
			// Parse error messages using the utility function
			const parsedResult = parseErrorMessages(value);
			fieldErrors = parsedResult.fieldErrors;
			errorMessage = parsedResult.generalError || '';
			// Set showErrorMessage to true when we have either field errors or general error
			showErrorMessage =
				Object.keys(parsedResult.fieldErrors).length > 0 || parsedResult.generalError !== null;
		},
		// Enhanced success behavior
		useToastOnSuccess: true
	};

	// Password rules
	const specialChars = '!@#$%^&*';
	function checkPasswordRules(password: string) {
		return {
			length: password.length > 8,
			lowercase: /[a-z]/.test(password),
			uppercase: /[A-Z]/.test(password),
			special: new RegExp(`[${specialChars.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&')}]`).test(
				password
			),
			number: /[0-9]/.test(password)
		};
	}

	$: passwordRulesStatus = checkPasswordRules(passwordFormData.new_password);

	$: allPasswordRulesPassed = Object.values(passwordRulesStatus).every((value) => value === true);
	$: passwordsMatch =
		passwordFormData.new_password === passwordFormData.confirm_password &&
		passwordFormData.new_password.length > 0;

	// Password visibility toggle functions
	function toggleCurrentPasswordVisibility() {
		showCurrentPassword = !showCurrentPassword;
	}

	function toggleNewPasswordVisibility() {
		showNewPassword = !showNewPassword;
	}

	function toggleConfirmPasswordVisibility() {
		showConfirmPassword = !showConfirmPassword;
	}

	// Standard enhance handler function (from UserSignUp.svelte)
	function handleEnhanceStandard(options: typeof enhanceOptions) {
		return async ({ result, update }: { result: any; update: () => Promise<void> }) => {
			// Set pending state
			options.setPending?.(true);
			isSubmitting = true;

			if (result.type === 'failure') {
				options.setErrorMessage?.(result.data?.error || 'Operation failed');
			} else if (result.type === 'success') {
				const successMessage = result.data?.res_msg || t('profile_change_success');

				if (options.useToastOnSuccess) {
					toastStore.add(t('profile_change_success'), 'success');
				}

				// Update language preference using the new centralized system
				try {
					languagePreference.setLanguage(formData.preferred_language as 'en' | 'th');
				} catch (error) {
					console.error('Failed to update language preference:', error);
					// Don't block the success flow if language update fails
				}

				// Update the page data
				// invalidateAll();
				window.location.reload();
			}

			// Reset pending state
			options.setPending?.(false);
		};
	}

	// Password enhance handler (keep existing logic for password modal)
	const handlePasswordEnhance = () => {
		return async ({ result, update }: { result: any; update: () => Promise<void> }) => {
			enhanceOptions.setPending(true, true);

			if (result.type === 'failure') {
				toastStore.add(result.data?.errors || 'Operation failed', 'error');
			} else if (result.type === 'success') {
				toastStore.add(t('password_change_success'), 'success');
				enhanceOptions.setModalOpen(false);
			}

			await update();
			enhanceOptions.setPending(false, true);
		};
	};

	// Watch modal state to clean up everything
	$: if (passwordModalOpen === false) {
		passwordFormData = {
			old_password: '',
			new_password: '',
			confirm_password: ''
		};
		passwordFieldsEverTyped = false;
		// Reset password visibility states
		showCurrentPassword = false;
		showNewPassword = false;
		showConfirmPassword = false;
	}
</script>

<div id="settings-user-profile-container" class="space-y-4 rounded-lg bg-white p-6 shadow-md">
	<form
		id="settings-user-profile-form"
		action="?/update_user"
		method="POST"
		use:enhance={() => handleEnhanceStandard(enhanceOptions)}
		class="space-y-4"
	>
		<div id="settings-user-profile-header" class="flex w-full items-center justify-between">
			<div>
				<h2 id="settings-user-profile-title" class="text-xl font-medium text-black">{t('signup_form_tab_personal_info')}</h2>
				<!-- <p class="text-sm text-gray-500">{t('your_info_description')}</p> -->
			</div>

			<Button
				id="settings-user-profile-save-btn"
				type="submit"
				color="green"
				class="disabled:cursor-not-allowed disabled:opacity-20"
				disabled={isSubmitting || !hasChanges}
			>
				{#if isSubmitting}
					<span class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2" />
					{t('saving')}
				{:else}
					<CheckOutline class="mr-2 h-4 w-4" />
					{t('save')}
				{/if}
			</Button>
		</div>

		<!-- General error message display -->
		{#if showErrorMessage && errorMessage}
			<Alert id="settings-user-profile-error-general" color="red" class="mb-4">
				{errorMessage}
			</Alert>
		{/if}

		<!-- Field-specific error display for multi-field errors -->
		{#if Object.keys(fieldErrors).length > 0}
			{#each Object.entries(fieldErrors) as [fieldName, errors]}
				{#each errors as error}
					<Alert id="settings-user-profile-error-{fieldName}" color="red" class="mb-4">
						<strong>{fieldName}:</strong>
						{error}
					</Alert>
				{/each}
			{/each}
		{/if}

		<!-- <div>
			<Label for="name" class="mb-1 text-left">{t('display_name')}</Label>
			<Input
				id="name"
				name="name"
				type="text"
				maxlength={20}
				bind:value={formData.name}
				on:input={(e) => handleNameInput(e, 'name')}
				required
			/>
			{#if fieldErrors.name}
				{#each fieldErrors.name as error}
					<Alert color="red" class="mt-1 px-3 py-2 text-sm">
						{error}
					</Alert>
				{/each}
			{/if}
		</div> -->

		<div id="settings-user-profile-name-section" class="grid grid-cols-2 gap-4 pl-4">
			<div>
				<Label for="settings-user-profile-first-name" class="mb-1 text-left">
					{t('first_name')}
					<span class="text-xs font-medium text-red-800">*</span>
				</Label>
				<Input
					id="settings-user-profile-first-name"
					name="first_name"
					type="text"
					maxlength={50}
					bind:value={formData.first_name}
					on:input={(e) => handleNameInput(e, 'first_name')}
					required
				/>
				{#if fieldErrors.first_name}
					{#each fieldErrors.first_name as error}
						<Alert color="red" class="mt-1 px-3 py-2 text-sm">
							{error}
						</Alert>
					{/each}
				{/if}
			</div>

			<div>
				<Label for="settings-user-profile-last-name" class="mb-1 text-left">
					{t('last_name')}
					<span class="text-xs font-medium text-red-800">*</span>
				</Label>
				<Input
					id="settings-user-profile-last-name"
					name="last_name"
					type="text"
					maxlength={50}
					bind:value={formData.last_name}
					on:input={(e) => handleNameInput(e, 'last_name')}
					required
				/>
				{#if fieldErrors.last_name}
					{#each fieldErrors.last_name as error}
						<Alert color="red" class="mt-1 px-3 py-2 text-sm">
							{error}
						</Alert>
					{/each}
				{/if}
			</div>
		</div>

		<div id="settings-user-profile-readonly-section" class="grid grid-cols-2 gap-4 pl-4">
			<div>
				<Label class="focus:text-blue text-left text-black">{t('username')}</Label>
				<Input id="settings-user-profile-username-display" placeholder={user.username} disabled />

			</div>
			<div>
				<Label class="focus:text-blue text-left text-black">{t('role')}</Label>
				<Input id="settings-user-profile-role-display" placeholder={user.roles[0].name} disabled />

			</div>
		</div>		

		<div id="settings-user-profile-password-section" class="grid grid-cols-2 gap-4 pl-4">
			<div>
				<Label for="password" class="focus:text-blue text-left text-black">{t('password')}</Label>
				<div class="flex gap-2">
					<!-- <Input
						id="password"
						name="password"
						type="password"
						value="••••••••••••••••••"
						disabled
						class="flex-1 bg-gray-100"
					/> -->
					<Button id="settings-user-profile-change-password-btn" type="button" color="green" on:click={() => (passwordModalOpen = true)}>
						{t('change_password')}
					</Button>
				</div>
			</div>
		</div>

		<!-- Additional Contact Information Section -->
		<div id="settings-user-profile-contact-section" class="space-y-4">
			<div>
				<h3 id="settings-user-profile-contact-title" class="text-lg font-medium text-black">{t('signup_form_tab_contact_details')}</h3>
				<!-- <p class="text-sm text-gray-500">Additional contact information</p> -->
			</div>

			<div class="grid grid-cols-2 gap-4 pl-4">
				<div>
					<Label for="settings-user-profile-work-phone" class="mb-1 text-left">{t('signup_form_work_phone')}</Label>
					<Input
						id="settings-user-profile-work-phone"
						name="work_phone"
						type="text"
						bind:value={formData.work_phone}
						maxlength={20}
						on:input={(e) => handlePhoneInput(e, 'work_phone')}
					/>
					{#if validationErrors.work_phone}
						<Alert color="red" class="mt-1 px-3 py-2 text-sm">
							{validationErrors.work_phone}
						</Alert>
					{/if}
					{#if fieldErrors.work_phone}
						{#each fieldErrors.work_phone as error}
							<Alert color="red" class="mt-1 px-3 py-2 text-sm">
								{error}
							</Alert>
						{/each}
					{/if}
				</div>
				<div>
					<Label for="settings-user-profile-work-email" class="mb-1 text-left">
						{t('signup_form_work_email')}
						<span class="text-xs font-medium text-red-800">*</span>
					</Label>
					<Input
						id="settings-user-profile-work-email"
						name="work_email"
						type="email"
						maxlength={30}
						bind:value={formData.work_email}
						on:input={(e) => handleEmailInput(e, 'work_email')}
					/>
					{#if validationErrors.work_email}
						<Alert color="red" class="mt-1 px-3 py-2 text-sm">
							{validationErrors.work_email}
						</Alert>
					{/if}
					{#if fieldErrors.work_email}
						{#each fieldErrors.work_email as error}
							<Alert color="red" class="mt-1 px-3 py-2 text-sm">
								{error}
							</Alert>
						{/each}
					{/if}
				</div>
			</div>

			<div class="grid grid-cols-2 gap-4 pl-4">
				<div>
					<Label for="settings-user-profile-personal-phone" class="mb-1 text-left"
						>{t('signup_form_personal_phone')}</Label
					>
					<Input
						id="settings-user-profile-personal-phone"
						name="personal_phone"
						type="text"
						bind:value={formData.personal_phone}
						maxlength={20}
						on:input={(e) => handlePhoneInput(e, 'personal_phone')}
					/>
					{#if validationErrors.personal_phone}
						<Alert color="red" class="mt-1 px-3 py-2 text-sm">
							{validationErrors.personal_phone}
						</Alert>
					{/if}
					{#if fieldErrors.personal_phone}
						{#each fieldErrors.personal_phone as error}
							<Alert color="red" class="mt-1 px-3 py-2 text-sm">
								{error}
							</Alert>
						{/each}
					{/if}
				</div>
				<div>
					<Label for="settings-user-profile-personal-email" class="mb-1 text-left"
						>{t('signup_form_personal_email')}</Label
					>
					<Input
						id="settings-user-profile-personal-email"
						name="personal_email"
						type="email"
						maxlength={30}
						bind:value={formData.personal_email}
						on:input={(e) => handleEmailInput(e, 'personal_email')}
					/>
					{#if validationErrors.personal_email}
						<Alert color="red" class="mt-1 px-3 py-2 text-sm">
							{validationErrors.personal_email}
						</Alert>
					{/if}
					{#if fieldErrors.personal_email}
						{#each fieldErrors.personal_email as error}
							<Alert color="red" class="mt-1 px-3 py-2 text-sm">
								{error}
							</Alert>
						{/each}
					{/if}
				</div>
			</div>
		</div>

		<!-- User Preferences Section -->
		<div id="settings-user-profile-preferences-section" class="space-y-4">
			<div>
				<h3 id="settings-user-profile-preferences-title" class="text-lg font-medium text-black">{t('signup_form_tab_preference')}</h3>
				<!-- <p class="text-sm text-gray-500">Your language and display preferences</p> -->
			</div>

			<div class="pl-4">
				<Label for="settings-user-profile-preferred-language" class="mb-1 text-left">
					{t('signup_form_preferred_language')}
				</Label>
				<Select
					id="settings-user-profile-preferred-language"
					name="preferred_language"
					bind:value={formData.preferred_language}
					items={languageOptions}
					placeholder={t('signup_form_preferred_language_placeholder')}
				/>
			</div>
		</div>

		<!-- Emergency Contact Section -->
		<div id="settings-user-profile-emergency-section" class="space-y-4">
			<div>
				<h3 id="settings-user-profile-emergency-title" class="text-lg font-medium text-black">{t('signup_form_tab_emergency_contact')}</h3>
				<!-- <p class="text-sm text-gray-500">Emergency contact information</p> -->
			</div>

			<div class="grid grid-cols-2 gap-4 pl-4">
				<div>
					<Label for="settings-user-profile-emergency-contact-name" class="mb-1 text-left">
						{t('signup_form_emergency_name')}
					</Label>
					<Input
						id="settings-user-profile-emergency-contact-name"
						name="emergency_contact_name"
						type="text"
						maxlength={50}
						bind:value={formData.emergency_contact_name}
						on:input={handleEmergencyNameInput}
					/>
					{#if fieldErrors.emergency_contact_name}
						{#each fieldErrors.emergency_contact_name as error}
							<Alert color="red" class="mt-1 px-3 py-2 text-sm">
								{error}
							</Alert>
						{/each}
					{/if}
				</div>
				<div>
					<Label for="settings-user-profile-emergency-contact-phone" class="mb-1 text-left">
						{t('signup_form_emergency_phone')}
					</Label>
					<Input
						id="settings-user-profile-emergency-contact-phone"
						name="emergency_contact_phone"
						type="tel"
						maxlength={20}
						bind:value={formData.emergency_contact_phone}
						on:input={handleEmergencyPhoneInput}
					/>
					{#if validationErrors.emergency_contact_phone}
						<Alert color="red" class="mt-1 px-3 py-2 text-sm">
							{validationErrors.emergency_contact_phone}
						</Alert>
					{/if}
					{#if fieldErrors.emergency_contact_phone}
						{#each fieldErrors.emergency_contact_phone as error}
							<Alert color="red" class="mt-1 px-3 py-2 text-sm">
								{error}
							</Alert>
						{/each}
					{/if}
				</div>

				<!-- <div>
					<Label for="emergency_contact_email" class="mb-1 text-left">
						{t('signup_form_emergency_email')}
					</Label>
					<Input
						id="emergency_contact_email"
						name="emergency_contact_email"
						type="email"
						maxlength={30}
						bind:value={formData.emergency_contact_email}
						on:input={(e) => handleEmailInput(e, 'emergency_contact_email')}
					/>
					{#if validationErrors.emergency_contact_email}
						<Alert color="red" class="mt-1 px-3 py-2 text-sm">
							{validationErrors.emergency_contact_email}
						</Alert>
					{/if}
					{#if fieldErrors.emergency_contact_email}
						{#each fieldErrors.emergency_contact_email as error}
							<Alert color="red" class="mt-1 px-3 py-2 text-sm">
								{error}
							</Alert>
						{/each}
					{/if}
				</div> -->
			</div>
		</div>

		<!-- For Debugging Section (Read-only) -->
		<!-- <div class="space-y-4">
			<div>
				<h3 class="text-lg font-medium text-gray-700">For Debugging</h3>
				<p class="text-sm text-gray-500">Read-only system fields</p>
			</div>

			<div class="grid grid-cols-2 gap-4">
				<div>
					<Label for="username_display" class="mb-1 text-left">{t('username')}</Label>
					<Input
						id="username_display"
						type="text"
						value={formData.username}
						disabled
						readonly
						class="bg-gray-100"
					/>
				</div>
				<div>
					<Label for="employee_id_display" class="mb-1 text-left">{t('employee_id')}</Label>
					<Input
						id="employee_id_display"
						type="text"
						value={formData.id}
						disabled
						readonly
						class="bg-gray-100"
					/>
				</div>
			</div>
		</div> -->

		<input type="hidden" name="is_active" value={formData.is_active} />
		<input type="hidden" name="id" value={formData.id} />
		<input type="hidden" name="username" value={formData.username} />
		<input type="hidden" name="employee_id" value={formData.employee_id} />
		<input type="hidden" name="department" value={formData.department} />
		<input type="hidden" name="role" value={formData.role} />
	</form>
</div>

<Modal id="settings-user-profile-password-modal" bind:open={passwordModalOpen} size="xs" outsideclose>
	<div id="settings-user-profile-password-modal-title" class="mb-4 text-2xl font-semibold text-black">{t('change_password')}</div>
	<div class="p-2">
		<form
			id="settings-user-profile-password-form"
			action="?/change_password"
			method="POST"
			use:enhance={handlePasswordEnhance}
			class="space-y-3"
		>
			<div class="grid gap-2">
				<Label for="settings-user-profile-old-password" class="space-y-2 text-left text-sm">{t('current_password')}</Label
				>
				<div class="relative">
					<Input
						id="settings-user-profile-old-password"
						name="old_password"
						type={showCurrentPassword ? 'text' : 'password'}
						bind:value={passwordFormData.old_password}
						placeholder={t('current_password_placeholder')}
						required
						class="pr-10"
					/>
					<button
						id="settings-user-profile-toggle-old-password"
						type="button"
						class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 hover:text-gray-700 focus:outline-none"
						on:click={toggleCurrentPasswordVisibility}
						aria-label={showCurrentPassword ? 'Hide current password' : 'Show current password'}
						tabindex="-1"
					>
						{#if showCurrentPassword}
							<EyeSlashSolid class="h-5 w-5" />
						{:else}
							<EyeSolid class="h-5 w-5" />
						{/if}
					</button>
				</div>
			</div>
			<div class="grid gap-2">
				<Label for="settings-user-profile-new-password" class="space-y-2 text-left text-sm">{t('new_password')}</Label>
				<div class="relative">
					<Input
						id="settings-user-profile-new-password"
						name="new_password"
						type={showNewPassword ? 'text' : 'password'}
						bind:value={passwordFormData.new_password}
						placeholder={t('new_password_placeholder')}
						required
						on:input={() => (passwordFieldsEverTyped = true)}
						class="pr-10"
					/>
					<button
						id="settings-user-profile-toggle-new-password"
						type="button"
						class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 hover:text-gray-700 focus:outline-none"
						on:click={toggleNewPasswordVisibility}
						aria-label={showNewPassword ? 'Hide new password' : 'Show new password'}
						tabindex="-1"
					>
						{#if showNewPassword}
							<EyeSlashSolid class="h-5 w-5" />
						{:else}
							<EyeSolid class="h-5 w-5" />
						{/if}
					</button>
				</div>
				<div>
					<div class="mb-1 text-xs font-normal text-gray-400">{t('password_validation_msg_1')}</div>
					<ul class="space-y-0 text-xs">
						<li class="flex items-center">
							<span
								class={passwordRulesStatus.length
									? 'text-green-600'
									: passwordFieldsEverTyped
										? 'text-red-600'
										: 'text-gray-400'}>{t('password_validation_msg_2')}</span
							>
						</li>
						<li class="flex items-center">
							<span
								class={passwordRulesStatus.lowercase && passwordRulesStatus.uppercase
									? 'text-green-600'
									: passwordFieldsEverTyped
										? 'text-red-600'
										: 'text-gray-400'}>{t('password_validation_msg_3')}</span
							>
						</li>
						<li class="flex items-center">
							<span
								class={passwordRulesStatus.number
									? 'text-green-600'
									: passwordFieldsEverTyped
										? 'text-red-600'
										: 'text-gray-400'}>{t('password_validation_msg_4')}</span
							>
						</li>
						<li class="flex items-center">
							<span
								class={passwordRulesStatus.special
									? 'text-green-600'
									: passwordFieldsEverTyped
										? 'text-red-600'
										: 'text-gray-400'}>{t('password_validation_msg_5')} ({specialChars})</span
							>
						</li>
					</ul>
				</div>
			</div>
			<div class="grid gap-2">
				<Label for="settings-user-profile-confirm-password" class="space-y-2 text-left text-sm"
					>{t('confirm_password')}</Label
				>
				<div class="relative">
					<Input
						id="settings-user-profile-confirm-password"
						name="confirm_password"
						type={showConfirmPassword ? 'text' : 'password'}
						bind:value={passwordFormData.confirm_password}
						placeholder={t('confirm_new_password_placeholder')}
						required
						on:input={() => (passwordFieldsEverTyped = true)}
						class="pr-10"
					/>
					<button
						id="settings-user-profile-toggle-confirm-password"
						type="button"
						class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 hover:text-gray-700 focus:outline-none"
						on:click={toggleConfirmPasswordVisibility}
						aria-label={showConfirmPassword ? 'Hide confirm password' : 'Show confirm password'}
						tabindex="-1"
					>
						{#if showConfirmPassword}
							<EyeSlashSolid class="h-5 w-5" />
						{:else}
							<EyeSolid class="h-5 w-5" />
						{/if}
					</button>
				</div>
				<div style="min-height:1em;" class="justify-left items-top flex">
					{#if passwordFieldsEverTyped && !passwordsMatch && passwordFormData.confirm_password.length > 0}
						<span class="text-left text-xs text-red-600"
							>{t('password_validation_msg_do_not_match')}</span
						>
					{/if}
				</div>
			</div>
			<div class="mt-6 flex items-center justify-end gap-2">
				<Button
					id="settings-user-profile-password-cancel-btn"
					color="light"
					on:click={() => {
						passwordModalOpen = false;
					}}
				>
					{t('cancel')}
				</Button>
				<Button
					id="settings-user-profile-password-submit-btn"
					type="submit"
					color="green"
					class="disabled:cursor-not-allowed disabled:opacity-20"
					disabled={!allPasswordRulesPassed || !passwordsMatch}
				>
					<CheckOutline class="mr-2 h-4 w-4" />
					{t('submit')}
				</Button>
			</div>
		</form>
	</div>
</Modal>
