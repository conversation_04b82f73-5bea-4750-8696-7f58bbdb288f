# ============================================
# Stage 1: Builder - Build the application
# ============================================
FROM node:22-bookworm AS builder

ARG BUILD_VERSION=local
ENV BUILD_VERSION=${BUILD_VERSION}

ARG WORKDIR=/src/app
ARG VENDORDIR=/src/vendor
WORKDIR ${WORKDIR}


# CREATE USER
ARG USERNAME=node
ARG USER_UID=1000
ARG USER_GID=$USER_UID

# # Create the user
# RUN groupadd --gid $USER_GID $USERNAME \
#     && useradd --uid $USER_UID --gid $USER_GID -m $USERNAME \
    #
    # [Optional] Add sudo support. Omit if you don't need to install software after connecting.
RUN apt-get update \
    && apt-get install -y sudo \
    && echo $USERNAME ALL=\(root\) NOPASSWD:ALL > /etc/sudoers.d/$USERNAME \
    && chmod 0440 /etc/sudoers.d/$USERNAME

RUN apt update
RUN apt install -y vim

COPY --chown=${USER_UID}:${USER_GID} . ${WORKDIR}
RUN chown -R ${USER_UID}:${USER_GID} ${WORKDIR}

# Clear apt for optimizing image size
RUN apt clean
RUN rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Inject dummy .env to make build pass
ENV PUBLIC_BACKEND_URL=http://dummy
ENV PUBLIC_LIFF_ID=http://dummy
ENV PUBLIC_FRONTEND_URL=http://dummy

# [Optional] Set the default user. Omit if you want to keep the default as root.
USER $USERNAME

# Increase Node.js memory limit for all subsequent 'npm' commands
ENV NODE_OPTIONS="--max-old-space-size=4096"

RUN npm install
RUN npm run build

# ============================================
# Stage 2: Production - Runtime environment
# ============================================
FROM node:22-bookworm-slim AS production

ARG BUILD_VERSION=local
ENV BUILD_VERSION=${BUILD_VERSION}

ARG WORKDIR=/src/app
WORKDIR ${WORKDIR}

# CREATE USER
ARG USERNAME=node
ARG USER_UID=1000
ARG USER_GID=$USER_UID

# Copy package files
COPY --chown=${USER_UID}:${USER_GID} package*.json ${WORKDIR}/

# # Install only production dependencies
# USER $USERNAME
# RUN npm ci --only=production

# Copy node_modules from builder stage (already has all dependencies)
# TODO: remove this and use npm ci above instead
COPY --from=builder --chown=${USER_UID}:${USER_GID} ${WORKDIR}/node_modules ${WORKDIR}/node_modules

# Copy built application from builder stage
COPY --from=builder --chown=${USER_UID}:${USER_GID} ${WORKDIR}/build ${WORKDIR}/build
COPY --from=builder --chown=${USER_UID}:${USER_GID} ${WORKDIR}/static ${WORKDIR}/static
COPY --from=builder --chown=${USER_UID}:${USER_GID} ${WORKDIR}/svelte.config.js ${WORKDIR}/svelte.config.js

# Set environment variables
ENV PUBLIC_BACKEND_URL=http://dummy
ENV PUBLIC_LIFF_ID=http://dummy
ENV PUBLIC_FRONTEND_URL=http://dummy

# Switch to non-root user for security
USER $USERNAME
CMD npm run serve:win