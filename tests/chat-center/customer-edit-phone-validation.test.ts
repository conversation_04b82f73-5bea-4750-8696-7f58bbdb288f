/**
 * End-to-End Test: Chat Center Customer Edit Validation with Inline Icons and Tooltips
 *
 * This test validates the inline validation functionality for all validated fields
 * in the CustomerEdit component (phone, email, national_id), specifically testing
 * the inline validation icon and tooltip implementation.
 *
 * SVELTE COMPONENTS TESTED:
 * - CustomerEdit.svelte - Modal form for editing customer profile information
 *   └── Phone input field with inline validation icon and tooltip
 *   └── Email input field with inline validation icon and tooltip
 *   └── National ID input field with inline validation icon and tooltip
 *   └── Phone input: customer-edit-phone-input
 *   └── Email input: customer-edit-email-input
 *   └── National ID input: customer-edit-national-id-input
 *   └── Validation icons: customer-edit-*-validation-icon
 *   └── Tooltips triggered by hovering over validation icons
 *
 * VALIDATION STATES TESTED FOR EACH FIELD:
 * 1. Phone: Neutral ('#'), <PERSON><PERSON> ('✓' - 10 digits), Invalid ('✗' - wrong length)
 * 2. Email: Neutral ('@'), <PERSON><PERSON> ('✓' - valid format), Invalid ('✗' - invalid format)
 * 3. National ID: Neutral ('#'), Valid ('✓' - 13 digits), Invalid ('✗' - wrong length)
 *
 * TOOLTIP FUNCTIONALITY TESTED:
 * - Tooltip appears on hover over validation icon for all fields
 * - Tooltip displays appropriate validation message for each state
 * - Tooltip positioning and styling consistency across fields
 * - Consistent spacing and layout with inline validation icons
 */

import { test, expect } from '@playwright/test';
import type { Page } from '@playwright/test';

test.use({ viewport: { width: 1920, height: 1080 } });

// Import authentication utilities
import { performLoginWithRedirectHandling } from '../utils/auth.utils';

// Utility function to select a chat from other-assigned tab
async function selectChatFromOtherAssignedTab(page: Page) {
	console.log('Selecting chat from Other Assigned tab...');

	// Wait for tab navigation to be visible
	await page.waitForSelector('nav', { timeout: 10000 });
	await expect(page.locator('nav button').first()).toBeVisible();

	// Try to find and click "Other Assigned" tab using multiple selector strategies
	const otherAssignedSelectors = [
		'button:has-text("Other Assigned")',
		'button:has-text("other-assigned")',
		'nav button:nth-child(4)', // Fallback based on typical tab order
		'button[data-testid*="other"]'
	];

	let otherAssignedTab = null;
	for (const selector of otherAssignedSelectors) {
		const element = page.locator(selector).first();
		if (await element.isVisible()) {
			otherAssignedTab = element;
			break;
		}
	}

	if (!otherAssignedTab) {
		// Fallback: try "My Assigned" if "Other Assigned" is not available
		console.log('Other Assigned tab not found, trying My Assigned as fallback...');
		const myAssignedSelectors = [
			'button:has-text("My Assigned")',
			'button:has-text("my-assigned")',
			'nav button:nth-child(1)'
		];

		for (const selector of myAssignedSelectors) {
			const element = page.locator(selector).first();
			if (await element.isVisible()) {
				otherAssignedTab = element;
				break;
			}
		}
	}

	if (!otherAssignedTab) {
		throw new Error('Could not find any suitable tab (Other Assigned or My Assigned)');
	}

	await otherAssignedTab.click();
	await page.waitForTimeout(2000); // Allow tab content to load

	// Wait for chat items to load with multiple selector strategies
	const chatItemSelectors = [
		'.divide-y button',
		'[data-testid="chat-item"]',
		'button[data-identity-id]',
		'#platform-list-chat-item'
	];

	let chatItemsFound = false;
	for (const selector of chatItemSelectors) {
		try {
			await page.waitForSelector(selector, { timeout: 5000 });
			chatItemsFound = true;
			break;
		} catch (error) {
			console.log(`Selector ${selector} not found, trying next...`);
		}
	}

	if (!chatItemsFound) {
		throw new Error('No chat items found in the selected tab');
	}

	// Select the first available chat item using the most reliable selector
	const chatItems = page.locator('.divide-y button, [data-testid="chat-item"]');
	await expect(chatItems.first()).toBeVisible();

	const firstChatItem = chatItems.first();
	await firstChatItem.click();
	await page.waitForTimeout(2000); // Allow conversation to load

	console.log('Successfully selected chat from tab');
}

// Utility function to navigate to customer information tab
async function navigateToInformationTab(page: Page) {
	console.log('Navigating to Information tab...');

	// Wait for customer info panel to be visible
	await expect(page.locator('[data-testid="customer-info-panel"]')).toBeVisible({ timeout: 10000 });

	// Click on Information tab using the specific ID from CustomerInfoPanel.svelte
	const informationTab = page.locator('#customer-info-customer-tab-information');
	await expect(informationTab).toBeVisible({ timeout: 10000 });
	await informationTab.click();
	await page.waitForTimeout(1000); // Allow tab content to load

	// Verify tab content is loaded
	const tabContent = page.locator('[data-testid="customer-tab-content-information"]');
	await expect(tabContent).toBeVisible({ timeout: 10000 });

	console.log('✓ Successfully navigated to Information tab');
}

// Utility function to open customer edit modal
async function openCustomerEditModal(page: Page) {
	console.log('Opening CustomerEdit modal...');

	// Wait for the CustomerEdit component button using the unique ID
	const editProfileButton = page.locator('#customer-edit-open-modal-button');
	await expect(editProfileButton).toBeVisible({ timeout: 10000 });
	await editProfileButton.click();
	await page.waitForTimeout(1000); // Wait for modal to open

	// Verify modal opens correctly
	const customerEditModal = page.locator('#customer-edit-modal');
	await expect(customerEditModal).toBeVisible({ timeout: 10000 });

	// Verify modal title is present
	const modalTitle = page.locator('#customer-edit-modal-title');
	await expect(modalTitle).toBeVisible();

	console.log('✓ CustomerEdit modal opened successfully');
	return customerEditModal;
}

test.describe('Chat Center Customer Edit Validation with Inline Icons and Tooltips', () => {
	test.beforeEach(async ({ page }) => {
		// Clear any existing cookies and ensure fresh state
		await page.context().clearCookies();
	});

	test('should display inline validation icons and tooltips for all validated fields', async ({ page }) => {
		// Step 1: Authentication and navigation to chat center
		await performLoginWithRedirectHandling(page);
		await expect(page).toHaveURL('/chat_center');
		
		// Verify page structure is loaded
		await expect(page.locator('#platform-list-chat-center-title').first()).toBeVisible();
		console.log('✓ Successfully navigated to chat center');

		// Step 2: Select chat from "Other Assigned" tab
		await selectChatFromOtherAssignedTab(page);
		
		// Step 3: Verify ConversationView loads with customer information
		await expect(page.locator('#info-tab-customer-name')).toBeVisible();
		console.log('✓ Conversation view loaded with customer information');

		// Step 4: Navigate to Information tab in CustomerInfoPanel
		await navigateToInformationTab(page);

		// Step 5: Open CustomerEdit modal
		const customerEditModal = await openCustomerEditModal(page);

		// Step 6: Test phone input field and validation icon
		const phoneInput = page.locator('#customer-edit-phone-input');
		await expect(phoneInput).toBeVisible({ timeout: 10000 });

		// Verify validation icon container is present
		const validationIconContainer = page.locator('#customer-edit-phone-validation-icon');
		await expect(validationIconContainer).toBeVisible();
		console.log('✓ Validation icon container is visible');

		// Step 7: Test neutral state (empty field)
		await phoneInput.clear();
		await page.waitForTimeout(500);

		// Check that neutral icon is displayed
		const neutralIcon = validationIconContainer.locator('.validation-icon.neutral');
		await expect(neutralIcon).toBeVisible();
		console.log('✓ Neutral validation icon displayed for empty field');

		// Step 8: Test invalid state (too short phone number)
		await phoneInput.fill('123');
		await page.waitForTimeout(500);

		// Check that invalid icon is displayed
		const invalidIcon = validationIconContainer.locator('.validation-icon.invalid');
		await expect(invalidIcon).toBeVisible();
		console.log('✓ Invalid validation icon displayed for short phone number');

		// Step 9: Test valid state (correct 10-digit phone number)
		await phoneInput.clear();
		await phoneInput.fill('0812345678');
		await page.waitForTimeout(500);

		// Check that valid icon is displayed
		const validIcon = validationIconContainer.locator('.validation-icon.valid');
		await expect(validIcon).toBeVisible();
		console.log('✓ Valid validation icon displayed for correct phone number');

		// Step 10: Test tooltip functionality
		// Hover over the validation icon to trigger tooltip
		await validationIconContainer.hover();
		await page.waitForTimeout(1000); // Wait for tooltip to appear

		// Check if tooltip is visible (Flowbite Svelte tooltips use role="tooltip")
		const tooltip = page.locator('[role="tooltip"]');
		await expect(tooltip).toBeVisible({ timeout: 5000 });
		console.log('✓ Tooltip appears on hover over validation icon');

		// Step 11: Verify input field has proper padding for icon
		const phoneInputStyles = await phoneInput.evaluate((el) => {
			const styles = window.getComputedStyle(el);
			return {
				paddingRight: styles.paddingRight
			};
		});

		// Verify that padding-right is applied (should be pr-10 which is 2.5rem or 40px)
		expect(phoneInputStyles.paddingRight).toBe('40px');
		console.log('✓ Phone input has proper right padding for validation icon');

		// Step 12: Test EMAIL field validation
		console.log('Testing email field validation...');
		const emailInput = page.locator('#customer-edit-email-input');
		await expect(emailInput).toBeVisible({ timeout: 10000 });

		// Verify email validation icon container is present
		const emailValidationIconContainer = page.locator('#customer-edit-email-validation-icon');
		await expect(emailValidationIconContainer).toBeVisible();
		console.log('✓ Email validation icon container is visible');

		// Test email neutral state (empty field)
		await emailInput.clear();
		await page.waitForTimeout(500);

		// Check that neutral icon is displayed (@)
		const emailNeutralIcon = emailValidationIconContainer.locator('.validation-icon.neutral');
		await expect(emailNeutralIcon).toBeVisible();
		console.log('✓ Email neutral validation icon (@) displayed for empty field');

		// Test email invalid state (invalid format)
		await emailInput.fill('invalid-email');
		await page.waitForTimeout(500);

		// Check that invalid icon is displayed
		const emailInvalidIcon = emailValidationIconContainer.locator('.validation-icon.invalid');
		await expect(emailInvalidIcon).toBeVisible();
		console.log('✓ Email invalid validation icon displayed for invalid format');

		// Test email valid state (correct format)
		await emailInput.clear();
		await emailInput.fill('<EMAIL>');
		await page.waitForTimeout(500);

		// Check that valid icon is displayed
		const emailValidIcon = emailValidationIconContainer.locator('.validation-icon.valid');
		await expect(emailValidIcon).toBeVisible();
		console.log('✓ Email valid validation icon displayed for correct format');

		// Test email tooltip functionality
		await emailValidationIconContainer.hover();
		await page.waitForTimeout(1000);

		// Check if tooltip is visible
		const emailTooltip = page.locator('[role="tooltip"]');
		await expect(emailTooltip).toBeVisible({ timeout: 5000 });
		console.log('✓ Email tooltip appears on hover over validation icon');

		// Step 13: Test NATIONAL ID field validation
		console.log('Testing national ID field validation...');
		const nationalIdInput = page.locator('#customer-edit-national-id-input');
		await expect(nationalIdInput).toBeVisible({ timeout: 10000 });

		// Verify national ID validation icon container is present
		const nationalIdValidationIconContainer = page.locator('#customer-edit-national-id-validation-icon');
		await expect(nationalIdValidationIconContainer).toBeVisible();
		console.log('✓ National ID validation icon container is visible');

		// Test national ID neutral state (empty field)
		await nationalIdInput.clear();
		await page.waitForTimeout(500);

		// Check that neutral icon is displayed (#)
		const nationalIdNeutralIcon = nationalIdValidationIconContainer.locator('.validation-icon.neutral');
		await expect(nationalIdNeutralIcon).toBeVisible();
		console.log('✓ National ID neutral validation icon (#) displayed for empty field');

		// Test national ID invalid state (too short)
		await nationalIdInput.fill('123456');
		await page.waitForTimeout(500);

		// Check that invalid icon is displayed
		const nationalIdInvalidIcon = nationalIdValidationIconContainer.locator('.validation-icon.invalid');
		await expect(nationalIdInvalidIcon).toBeVisible();
		console.log('✓ National ID invalid validation icon displayed for short ID');

		// Test national ID valid state (correct 13 digits)
		await nationalIdInput.clear();
		await nationalIdInput.fill('1234567890123');
		await page.waitForTimeout(500);

		// Check that valid icon is displayed
		const nationalIdValidIcon = nationalIdValidationIconContainer.locator('.validation-icon.valid');
		await expect(nationalIdValidIcon).toBeVisible();
		console.log('✓ National ID valid validation icon displayed for correct 13-digit ID');

		// Test national ID tooltip functionality
		await nationalIdValidationIconContainer.hover();
		await page.waitForTimeout(1000);

		// Check if tooltip is visible
		const nationalIdTooltip = page.locator('[role="tooltip"]');
		await expect(nationalIdTooltip).toBeVisible({ timeout: 5000 });
		console.log('✓ National ID tooltip appears on hover over validation icon');

		// Step 14: Verify consistent input field padding across all validated fields
		const emailInputStyles = await emailInput.evaluate((el) => {
			const styles = window.getComputedStyle(el);
			return { paddingRight: styles.paddingRight };
		});

		const nationalIdInputStyles = await nationalIdInput.evaluate((el) => {
			const styles = window.getComputedStyle(el);
			return { paddingRight: styles.paddingRight };
		});

		// All validated fields should have the same padding-right (pr-10 = 40px)
		expect(emailInputStyles.paddingRight).toBe('40px');
		expect(nationalIdInputStyles.paddingRight).toBe('40px');
		console.log('✓ All validated input fields have consistent right padding for validation icons');

		// Step 15: Close modal
		const cancelButton = page.locator('#customer-edit-cancel-button');
		await expect(cancelButton).toBeVisible();
		await cancelButton.click();
		await page.waitForTimeout(1000);

		// Verify modal closes
		await expect(customerEditModal).not.toBeVisible({ timeout: 10000 });
		console.log('✓ Modal closed successfully');

		console.log('🎉 All validation fields (phone, email, national_id) with inline icons and tooltips test completed successfully!');
	});
});
